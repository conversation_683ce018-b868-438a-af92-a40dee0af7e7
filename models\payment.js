const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Payment extends Model {
    static associate(models) {

      Payment.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });
      Payment.belongsTo(models.Registration, {
        foreignKey: "registrationId",
        as: "registration",
      });
      Payment.belongsTo(models.BulkRegistration, {
        foreignKey: "bulkRegistrationId",
        as: "bulkRegistration",
      });
      Payment.hasMany(models.Registration, { foreignKey: 'payment_id', as: 'registrations' });

      Payment.belongsTo(models.User, { foreignKey: "userId", as: "user" });
    }
  }

  Payment.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: 'user_id',
      },
      tournamentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "tournament",
          key: "id",
        },
        field: 'tournament_id',
      },
      registrationId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "registrations",
          key: "id",
        },
        field: 'registration_id',
      },
      bulkRegistrationId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "bulk_registrations",
          key: "id",
        },
        field: 'bulk_registration_id',
      },

      paymentDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'payment_date',
      },
      paymentStatus: {
        type: DataTypes.ENUM("pending", "paid", "failed"),
        allowNull: true,
        defaultValue: null,
        field: 'payment_status',
      },
      paymentTransactionId: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_transaction_id',
      },
      paymentAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        field: 'payment_amount',
      },
      paymentCurrency: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_currency',
      },
      paymentMethod: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_method',
      },
      paymentReference: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_reference',
      },
      paymentType: {
        type: DataTypes.ENUM("player", "club"),
        allowNull: true,
        field: 'payment_type',
      },
      paymentRemarks: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'payment_remarks',
      },
      paymentResponse: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: 'payment_response',
      },
    },
    {
      sequelize,
      modelName: "Payment",
      tableName: "payments",
    }
  );

  return Payment;
};
