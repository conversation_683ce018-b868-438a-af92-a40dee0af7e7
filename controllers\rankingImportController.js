const fs = require("fs");
const path = require("path");
const { processExcelFile } = require("../scripts/excel-extractor");
const { models } = require("../config/db");
const multer = require("multer");
const { v4: uuidv4 } = require("uuid");
const { Op } = require("sequelize");

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../uploads");
    // Create the uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate a unique filename to prevent overwriting
    const uniqueFilename = `${Date.now()}-${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  },
});

// Create the multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Accept only Excel files
    if (
      file.mimetype ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel files are allowed"), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB file size limit
  },
});

// Controller methods
const rankingImportController = {
  /**
   * Upload and process an Excel file
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async uploadAndProcess(req, res) {
    try {
      // The file is available at req.file due to multer middleware
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      const filePath = req.file.path;
      const tournament_id = req.body.tournament_id;
      const round_id = req.body.round_id ? parseInt(req.body.round_id) : 1;
      const age_category = req.body.age_category || "open";
      const gender_category = req.body.gender_category || "open";

      if (!tournament_id) {
        return res.status(400).json({
          success: false,
          message: "tournament_id is required",
        });
      }
      // getting tournament id from Tournament table
      const tournament = await models.Tournament.findOne({
        where: { title: tournament_id },
        attributes: ["id", "number_of_rounds"],
      });

      if (!tournament) {
        return res.status(404).json({
          success: false,
          message: "Tournament not found",
        });
      }

      // Check if records already exist for this tournament and round
      const existingRound = await models.Ranking.findOne({
        where: { 
          round_id: round_id, 
          tournament_id: tournament.id,
          age_category: age_category,
          gender_category: gender_category
        },
      });

      // If records exist, delete them instead of returning 409
      if (existingRound) {
        await models.Ranking.destroy({
          where: { 
            round_id: round_id, 
            tournament_id: tournament.id,
            age_category: age_category,
            gender_category: gender_category
          },
        });
        console.log(`Deleted existing rankings for tournament ${tournament.id}, round ${round_id}`);
      }
      
      // Process the Excel file
      const result = await processExcelFile(filePath, {
        tournament_id: tournament.id,
        round_id,
        age_category,
        gender_category,
        saveToDb: true,
        modelName: "Ranking",
      });

      // Delete the file after processing (optional)
      if (req.body.deleteAfterProcessing === "true") {
        fs.unlinkSync(filePath);
      }

      return res.status(200).json({
        success: true,
        message: existingRound ? "Existing rankings overwritten successfully" : "File processed successfully",
        data: {
          recordsProcessed: result.data ? result.data.length : 0,
          filename: req.file.originalname,
          tournament_id,
          round_id,
          age_category,
          gender_category,
        },
      });
    } catch (error) {
      console.error("Error processing file:", error);
      return res.status(500).json({
        success: false,
        message: `Error processing file: ${error.message}`,
      });
    }
  },

  /**
   * Get all rankings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAll(req, res) {
    try {
      const {
        tournament_id,
        round_id,
        category,
        limit = 100,
        offset = 0,
      } = req.query;

      const query = {
        where: {},
      };

      if (tournament_id) {
        query.where.tournament_id = tournament_id;
      }

      if (round_id) {
        query.where.round_id = parseInt(round_id);
      }

      if (category) {
        query.where.category = category;
      }

      query.limit = parseInt(limit);
      query.offset = parseInt(offset);
      query.order = [["rank", "ASC"]];

      const rankings = await models.Ranking.findAndCountAll(query);

      return res.status(200).json({
        success: true,
        data: rankings.rows,
        count: rankings.count,
        limit: query.limit,
        offset: query.offset,
      });
    } catch (error) {
      console.error("Error fetching rankings:", error);
      return res.status(500).json({
        success: false,
        message: `Error fetching rankings: ${error.message}`,
      });
    }
  },

  /**
   * Get a single ranking by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      
      const { round, page, ageCategory, genderCategory, playerName } =
        req.query;

      if (!id || !round) {
        return res.status(404).json({
          success: false,
          message: "id and round is required",
        });
      }
      const newTitle = decodeURIComponent(id)

      const currentPage = parseInt(page) || 1;
      const limit = 10;
      const offset = (currentPage - 1) * limit;
      const tournament = await models.Tournament.findOne({
        where: { title: newTitle },
        attributes: ["id", "number_of_rounds"],
      });
      if (!tournament) {
        return res.status(404).json({
          success: false,
          message: "Tournament not found",
        });
      }
      const where = {
        tournament_id: tournament.id,
        round_id: round,
      };

      if (playerName) where.player_name = { [Op.iLike]: `%${playerName}%` };

      if (ageCategory) {
        where.age_category = ageCategory;
      }
      if (genderCategory) {
        where.gender_category = genderCategory;
      }

      const { rows: ranking, count } = await models.Ranking.findAndCountAll({
        where,
        order: ["rank"],
        limit,
        offset,
      });

      const totalCount = count;
      const totalPages = Math.ceil(totalCount / limit);

      if (!ranking) {
        return res.status(404).json({
          success: false,
          message: "Ranking not found",
        });
      }

      return res.status(200).json({
        success: true,

        data: ranking,
        Pagination: {
          currentPage,
          totalPages,
        },
      });
    } catch (error) {
      console.error("Error fetching ranking:", error);
      return res.status(500).json({
        success: false,
        message: `Error fetching ranking: ${error.message}`,
      });
    }
  },

  /**
   * Get a single ranking by PlayerName
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getByName(req, res) {
    try {
      const { name, ageCategory, genderCategory } = req.query;

      if (!name) {
        return res.status(404).json({
          success: false,
          message: "name is required",
        });
      }

      const ranking = await models.Ranking.findOne({
        where: { player_name: name },
      });

      if (!ranking) {
        return res.status(404).json({
          success: false,
          message: "Ranking not found",
        });
      }
      return res.status(200).json({
        success: true,
        data: ranking,
      });
    } catch (error) {
      console.error("Error fetching ranking:", error);
      return res.status(500).json({
        success: false,
        message: `Error fetching ranking: ${error.message}`,
      });
    }
  },

  /**
   * Delete a ranking by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteById(req, res) {
    try {
      const { id } = req.params;

      const ranking = await models.Ranking.findByPk(id);

      if (!ranking) {
        return res.status(404).json({
          success: false,
          message: "Ranking not found",
        });
      }

      await ranking.destroy();

      return res.status(200).json({
        success: true,
        message: "Ranking deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting ranking:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting ranking: ${error.message}`,
      });
    }
  },

  /**
   * Delete all rankings for a tournament
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteByTournament(req, res) {
    try {
      const { tournament_id } = req.params;

      const result = await models.Ranking.destroy({
        where: { tournament_id },
      });

      return res.status(200).json({
        success: true,
        message: `${result} rankings deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting rankings:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting rankings: ${error.message}`,
      });
    }
  },

  /**
   * Delete all rankings for a tournament and round
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteByTournamentAndRound(req, res) {
    try {
      const { tournament_id, round_id } = req.params;

      const result = await models.Ranking.destroy({
        where: {
          tournament_id,
          round_id: parseInt(round_id),
        },
      });

      return res.status(200).json({
        success: true,
        message: `${result} rankings deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting rankings:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting rankings: ${error.message}`,
      });
    }
  },

  async getCurrentRound(req, res) {
    try {
      const { id } = req.query;
      const newTitle = decodeURIComponent(id)

      if (!id) {
        return res.status(404).json({
          success: false,
          message: "id is required",
        });
      }
      const tournament = await models.Tournament.findOne({
        where: { title: newTitle },
        attributes: ["id", "number_of_rounds"],
      });
      if (!tournament) {
        return res.status(404).json({
          success: false,
          message: "Tournament not found",
        });
      }
      const result = await models.Ranking.findAll({
        where: { tournament_id: tournament.id },
        attributes: ["round_id"]
      });

      if (!result || result.length === 0) {
        return res.status(200).json({
          success: true,
          currentRound: null,
          message: "No rounds conducted yet",
        });
      }

      const roundIds = result.map(r => r.round_id);
      const currentRound = roundIds.length > 0 ? Math.max(...roundIds) : 0;

      return res.status(200).json({
        success: true,
        currentRound: currentRound,
      });
    } catch (error) {
      console.error("Error deleting rankings:", error);
      return res.status(500).json({
        success: false,
        message: `Error deleting rankings: ${error.message}`,
      });
    }
  },

};

// Export the controller and the multer upload middleware
module.exports = {
  controller: rankingImportController,
  uploadMiddleware: upload,
};
