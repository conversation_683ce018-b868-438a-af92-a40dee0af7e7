const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Content extends Model {
    static associate(models) {
      // Define associations here if needed in the future
    }
  }

  Content.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      slug: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      htmlContent: {
        type: DataTypes.TEXT,
        allowNull: false,
        field: 'html_content',
      },
    },
    {
      sequelize,
      modelName: "Content",
      tableName: "contents",
      timestamps: true,
    }
  );

  return Content;
};
