const { env } = require("process");
const { config } = require("../config/config");
const PayU = require("payu-websdk");
// Make sure config.payu_key and config.payu_salt are properly defined
// Properly initialize the PayU SDK
let payuSDK;
try {
  let payuClient = new PayU({ key: config.payu_key, salt: config.payu_salt });
  // payuSDK = require("payu-websdk")({ key: config.payu_key, salt: config.payu_salt });
  payuSDK = payuClient;
} catch (error) {
  console.error("Error initializing PayU SDK:", error);
  // Fallback to original implementation if SDK fails
  payuSDK = null;
}

/**
 * Generate hash for PayU payment
 * @param {Object} params - Payment parameters
 * @returns {string} - Generated hash
 */
const generateHash = (params) => {
  const {
    key,
    txnid,
    amount,
    productinfo,
    firstname,
    email,
    udf1,
    udf2,
    udf3,
    udf4,
    udf5,
    udf6,
    udf7,
    udf8,
    udf9,
    udf10,
  } = params;
  try {
    const hash = payuSDK.hasher.generatePaymentHash({
      txnid,
      amount: Number(amount).toFixed(2),
      productinfo,
      firstname,
      email,
      udf1: udf1 || "",
      udf2: udf2 || "",
      udf3: udf3 || "",
      udf4: udf4 || "",
      udf5: udf5 || "",
      udf6: udf6 || "",
      udf7: udf7 || "",
      udf8: udf8 || "",
      udf9: udf9 || "",
      udf10: udf10 || "",
    });
    return hash;
  } catch (error) {
    console.error("SDK hash generation error:", error);
    // Fall back to manual implementation
  }
};

/**
 * Verify PayU payment response hash
 * @param {Object} params - Response parameters from PayU
 * @returns {boolean} - Whether the hash is valid
 */
const verifyPaymentResponse = (params) => {
  const { hash: receivedHash } = params;

  try {
    const isValid = payuSDK.hasher.validateResponseHash({
      txnid: params.txnid,
      amount: params.amount,
      productinfo: params.productinfo,
      firstname: params.firstname,
      email: params.email,
      status: params.status,
      udf1: params.udf1 || "",
      udf2: params.udf2 || "",
      udf3: params.udf3 || "",
      udf4: params.udf4 || "",
      udf5: params.udf5 || "",
      udf6: params.udf6 || "",
      udf7: params.udf7 || "",
      udf8: params.udf8 || "",
      udf9: params.udf9 || "",
      udf10: params.udf10 || "",
      hash: receivedHash,
    });
    console.log("Hash verification result:", isValid);
    return isValid;
  } catch (error) {
    console.error("SDK hash verification error:", error);
    // Fall back to manual implementation
  }
};

// Keep the rest of your code as is
const generatePaymentFormData = (paymentData) => {
  // Your existing implementation
  const {
    amount,
    productinfo,
    firstname,
    email,
    phone,
    txnid,
    surl,
    furl,
    udf1,
    udf2,
    udf3,
    udf4,
    udf5,
    udf6,
    udf7,
    udf8,
    udf9,
    udf10,
  } = paymentData;

  const key = config.payu_key;
  const formattedAmount = parseFloat(amount).toFixed(2);

  const params = {
    key,
    txnid,
    amount: formattedAmount,
    productinfo,
    firstname,
    email,
    udf1,
    udf2,
    udf3,
    udf4,
    udf5,
    udf6,
    udf7,
    udf8,
    udf9,
    udf10,
  };

  const hash = generateHash(params);
  console.log("Generated hash:", hash);

  const formData = {
    key,
    txnid,
    amount: formattedAmount,
    productinfo,
    firstname,
    email,
    phone,
    surl,
    furl,
    hash,
    udf1: udf1 || "",
    udf2: udf2 || "",
    udf3: udf3 || "",
    udf4: udf4 || "",
    udf5: udf5 || "",
    udf6: udf6 || "",
    udf7: udf7 || "",
    udf8: udf8 || "",
    udf9: udf9 || "",
    udf10: udf10 || "",
  };
  return formData;
};

module.exports = {
  generateHash,
  verifyPaymentResponse,
  generatePaymentFormData,
};
