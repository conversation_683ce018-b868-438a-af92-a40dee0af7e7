'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Notifications_type') THEN
          CREATE TYPE "enum_Notifications_type" AS ENUM (
            'tournament-registration',
            'tournament-withdraw',
            'tournament-pairing',
            'tournament-results',
            'promotional',
            'payment-confirmation',
            'tournament-reminder'
          );
        ELSE
          ALTER TYPE "enum_Notifications_type" ADD VALUE IF NOT EXISTS 'tournament-reminder';
        END IF;
      END $$;
    `);
  },

  async down(queryInterface, Sequelize) {
    // NOTE: PostgreSQL doesn't support removing enum values directly.
    // You would need to recreate the ENUM type without 'tournament-reminder'.
    // Be cautious here!
  }
};
