"use strict";
const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class User extends Model {
    static associate(models) {
      // Define associations here
      User.hasOne(models.ClubDetail, {
        foreignKey: "userId",
        onDelete: "CASCADE",
      });
      User.hasMany(models.Tournament, {
        foreignKey: "clubId",
      });
      User.hasOne(models.PlayerDetail, {
        foreignKey: "userId",
        onDelete: "CASCADE",
      });
      User.hasMany(models.Registration, {
        foreignKey: "playerId", as: "registrations",
      });
      User.hasMany(models.Payment, {
        foreignKey: "userId",
      });
      User.hasOne(models.Bankdetails, {
        foreignKey: "clubId",
        onDelete: "CASCADE",
      });
      User.hasMany(models.InviteRequest, {
        foreignKey: "userId",
      });
      User.hasOne(models.ArbiterDetails, {
        foreignKey: "userId",
        onDelete: "CASCADE",
      });
    
      
    }
  }

  User.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      cbid: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
      },
      phoneNumber: {
        type: DataTypes.STRING(15),
        allowNull: false,
        unique: true,
        field: 'phone_number',
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      role: {
        type: DataTypes.ENUM("admin", "club", "player","arbiter"),
        allowNull: false,
        defaultValue: "player",
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        field: 'is_active',
      },
    },
    {
      sequelize,
      modelName: "User",
      tableName: "users",
    }
  );

  return User;
};
