const { Sequelize } = require("sequelize");
const { database, config } = require("./config");
const initModels = require("../models/model");

const sequelizeOptions = {
  dialectOptions: config.database.dialectOptions,
  logging: console.log, // Set this to false to disable logging
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};
// Destructure database configuration
// const Tournament = initTounrament(Sequelize);

const sequelize = new Sequelize(config.databaseUrl, {
  dialect: "postgres",
  ...sequelizeOptions, // Spread the options here`
});

const models = initModels(sequelize);
Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models); // Pass models object to associate
  }
});
const connectDb = async (options = {}) => {
  try {
    await sequelize.authenticate();
    // Sync models with the database
    if (options.force) {
      // This will drop all tables and recreate them
      await sequelize.sync({ force: true });
    } else if (options.alter) {
      // This will alter existing tables to match the model
      await sequelize.sync({ alter: true });
    } else {
      // This will create tables if they don't exist
      await sequelize.sync();
      console.log(
        "Database synchronized: Tables created if they didn't exist."
      );
    }

    // If seed option is provided, seed the database
    if (options.seed) {
      const seedDatabase = require("../utils/seed");
      await seedDatabase(models, {
        ignoreDuplicates: true,
      });
    }
  } catch (error) {
    console.error("Unable to connect to the database:", error);
    throw error; // Re-throw the error for proper error handling
  }
};

module.exports = { sequelize, connectDb, models };
