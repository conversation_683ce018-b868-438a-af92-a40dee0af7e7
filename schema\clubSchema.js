const z = require("zod");
const clubDetailSchema = z.object({
  clubName: z
    .string()
    .min(2, "Club Name is required")
    .max(100, "Club Name is too long"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  district: z.string().min(1, "District is required"),
  city: z.string().min(1, "City is required"),
  pincode: z.string().regex(/^\d{6}$/, "Invalid pincode"),
  address: z.string().min(10, "Address is required"),
  locationUrl: z.string().url("Invalid URL format").optional(),
  authorizedSignatoryName: z
    .string()
    .min(2, "Authorized Signatory Name is required"),
  authorizedSignatoryContactNumber: z
    .string()
    .regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, "Invalid mobile number"),
  authorizedSignatoryEmail: z.string().email("Invalid email format"),
  authorizedSignatoryDesignation: z
    .string()
    .min(2, "Authorized Signatory Designation is required"),
  contactPersonName: z.string().min(2, "Contact Person Name is required"),
  contactPersonNumber: z
    .string()
    .regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, "Invalid mobile number"),
  alternateContactNumber: z
    .string()
    .regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, "Invalid mobile number")
    .optional(),
  contactPersonEmail: z.string().email("Invalid email format"),
  profileUrl: z.string().url("Invalid URL format").optional(),
  clubDistrictId: z.string().min(1, "Club District ID is required"),
});
const getAllClubSchema = z.object({
  clubName: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  district: z.string().optional(),
  city: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  mobile: z.string().optional(),
  mail:z.string().optional(),
});

module.exports = { clubDetailSchema, getAllClubSchema };
