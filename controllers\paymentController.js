const { v4: uuidv4 } = require("uuid");
const { config } = require("../config/config");
const { sendResponse, handleError } = require("../utils/apiResponse");
const {
  generatePaymentFormData,
  verifyPaymentResponse,
} = require("../utils/payuPayment");
const {
  Payment,
  Registration,
  Tournament,
  User,
  PlayerDetail,
  ClubDetail,
  Notifications,
  BulkRegistration,
} = require("../config/db").models;
const emailService = require("../utils/mailer/emailService");
const { sequelize } = require("../config/db");
const { z } = require("zod");
const { Op } = require("sequelize");
const { CheckEligibleCategory } = require("../utils/elegibleCategory");
const smsService = require("../utils/sms/smsService");
const notificationService = require("../services/notificationService");
const cronService = require("../services/cronService");

const PAYMENT_STATUS = {
  PENDING: "pending",
  PAID: "paid",
  FAILED: "failed",
};

/**
 * Initiate a payment for tournament registration
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const initiatePayment = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tournamentId, ageCategory, genderCategory } = req.body;

    if (!userId || !tournamentId || !ageCategory || !genderCategory) {
      return sendResponse(res, 400, {
        success: false,
        error: "Missing required parameters",
      });
    }

    // Get user details
    const user = await User.findOne({
      where: { id: userId },
      include: [
        {
          model: PlayerDetail,
          attributes: ["dob", "gender"],
        },
      ],
    });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: { id: tournamentId },
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    const { eligible, reason } = CheckEligibleCategory(
      (tournamentCategory = tournament.tournamentCategory),
      user.PlayerDetail.dob,
      user.PlayerDetail.gender,
      ageCategory,
      genderCategory
    );

    if (!eligible) {
      return sendResponse(res, 400, {
        success: false,
        error: reason || "You are not eligible for this tournament",
        action: "notEligible",
      });
    }

    // Create a new payment record
    const txnid = `CB-${uuidv4().substring(0, 8)}`;
    const amount = parseFloat(tournament.entryFee).toFixed(2);
    const productinfo = `Registration for ${tournament.title}`;
    const firstname = user.name;
    const email = user.email;
    const phone = user.phoneNumber;

    // Create payment in database
    const payment = await Payment.create({
      userId: userId,
      tournamentId,
      paymentTransactionId: txnid,
      paymentAmount: amount,
      paymentCurrency: "INR",
      paymentType: "player",
      paymentStatus: PAYMENT_STATUS.PENDING,
      paymentMethod: "PayU",
      paymentRemarks: `Payment for ${tournament.title}`,
    });

    // Generate PayU payment form data
    const baseUrl = config.backend_url;
    const surl = `${baseUrl}/api/v1/payment/success`;
    const furl = `${baseUrl}/api/v1/payment/failure`;

    const paymentData = {
      amount,
      productinfo,
      firstname,
      email,
      phone,
      txnid,
      surl,
      furl,
      udf1: userId,
      udf2: tournamentId,
      udf3: payment.id,
      udf4: tournament.title,
      udf5: `${genderCategory}-${ageCategory}`,
    };

    const formData = generatePaymentFormData(paymentData);

    return sendResponse(res, 200, {
      success: true,
      message: "Payment initiated",
      data: {
        paymentId: payment.id,
        formData,
        paymentUrl: config.payu_payment_url,
      },
    });
  } catch (error) {
    console.error("Error in initiatePayment:", error);
    handleError(res, error);
  }
};

/**
 * Handle payment success callback from PayU
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const paymentSuccess = async (req, res) => {
  try {
    const paymentResponse = req.body;
    // Verify the payment response hash
    console.log(paymentResponse);
    const isValid = verifyPaymentResponse(paymentResponse);
    console.log(isValid);

    if (!isValid) {
      console.error("Invalid payment response hash");
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=invalid_hash`
      );
    }

    const {
      txnid,
      status,
      amount,
      mode,
      mihpayid,
      bank_ref_num,
      udf1: userId,
      udf2: tournamentId,
      udf3: paymentId,
      udf4: tournamentTitle,
      udf5: eligibleCategory,
    } = paymentResponse;

    const [genderCategory, ageCategory] = eligibleCategory.split("-");

    const t = await sequelize.transaction();
    try {
      let tournament;
      const payment = await Payment.findOne({
        where: { id: paymentId },
        transaction: t,
      });
      if (!payment) {
        throw new Error("Payment not found");
      }

      await payment.update(
        {
          paymentStatus:
            status === "success" ? PAYMENT_STATUS.PAID : PAYMENT_STATUS.FAILED,
          paymentMethod: mode,
          paymentReference: mihpayid,
          paymentRemarks: bank_ref_num,

          paymentResponse: paymentResponse,
        },
        { transaction: t }
      );

      if (status === "success") {
        // Get user and tournament details for email
        const user = await User.findByPk(userId, { transaction: t });
        tournament = await Tournament.findByPk(
          tournamentId,
          {
            include: [
              {
                model: User,
                as: "club",
                attributes: ["name"],
              },
            ],
          },
          {
            transaction: t,
          }
        );
        if (!tournament) {
          throw new Error("Tournament not found");
        }
        const tournamentName = tournament.title;
        const clubName = tournament.club.name;
        const tournamentDate = tournament.startDate;
        const registrationCount = await Registration.count({
          where: {
            tournamentId: tournamentId,
          },
          transaction: t,
        });

        // Generate registration ID with the count
        const regId = generateRegistrationId(
          tournamentName,
          clubName,
          tournamentDate,
          registrationCount
        );

        const registration = await Registration.create(
          {
            tournamentId,
            playerId: userId,
            paymentId: paymentId,
            regId: regId,
            paymentAmount: amount,
            genderCategory,
            ageCategory,
            tournamentTitle,
            paymentDate: new Date(),
          },
          { transaction: t }
        );
        await payment.update(
          {
            registrationId: registration.id,
          },
          { transaction: t }
        );

        // Commit the transaction before sending email
        await t.commit();

        // Send payment confirmation email

        try {
          // await emailService.sendPaymentConfirmationEmail(
          //   payment.toJSON(),
          //   user,
          //   tournament,
          //   registration
          // );
          const smsData = {
            transactionId: txnid,
            paymentAmount: amount,
            mobile: user.phoneNumber,
            tournamentTitle,
          };

          const sentSms = await smsService.sendPaymentConfirmationSMS(smsData);
          const registerSms = await smsService.sendRegistrationConfirmationSMS({
            mobile: user.phoneNumber,
            tournamentTitle: tournament.title,
            startDate: tournament.startDate,
          });
          const bulkCreate = await Notifications.bulkCreate([
            {
              userId: userId,
              phoneNumber: user.phoneNumber,
              type: "tournament-registration",
              platform: "sms",
              status: "delivered",
              deliveryAttempts: 1,
              sentAt: new Date(),
              metadata: JSON.stringify(registerSms),
            },
            {
              userId: userId,
              phoneNumber: user.phoneNumber,
              type: "payment-confirmation",
              platform: "sms",
              status: "delivered",
              deliveryAttempts: 1,
              sentAt: new Date(),
              metadata: JSON.stringify(sentSms),
            },
          ]);
        } catch (emailError) {
          console.error(
            "Error sending payment confirmation email:",
            emailError
          );
          // Continue with the response even if email fails
        }
      } else {
        await t.commit();
      }

      return res.redirect(
        `${config.frontend_url}/payment-success?txnid=${txnid}&amount=${amount}&tournament=${tournament.title}`
      );
    } catch (err) {
      await t.rollback();
      throw err;
    }
  } catch (error) {
    console.error("Error in paymentSuccess:", error);

    return res.redirect(
      `${config.frontend_url}/payment-failure?error=server_error`
    );
  }
};

/**
 * Handle payment failure callback from PayU
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const paymentFailure = async (req, res) => {
  try {
    const paymentResponse = req.body;
    // Verify the payment response hash
    const isValid = verifyPaymentResponse(paymentResponse);

    if (!isValid) {
      console.error("Invalid payment response hash");
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=invalid_hash`
      );
    }

    const {
      txnid,
      status,
      error_Message,
      udf1: userId,
      udf2: tournamentId,
      udf3: paymentId,
      udf4: tournamentTitle,
      udf5: eligibleCategory,
    } = paymentResponse;

    // Update payment record
    const payment = await Payment.findOne({
      where: { id: paymentId },
    });

    if (!payment) {
      console.error("Payment not found:", paymentId);

      return res.redirect(
        `${config.frontend_url}/payment-failure?error=payment_not_found`
      );
    }

    // Update payment details
    await payment.update({
      paymentStatus: PAYMENT_STATUS.FAILED,
      paymentRemarks: error_Message,
      paymentResponse: paymentResponse,
    });

    // Get user and tournament details for email
    try {
      const user = await User.findByPk(userId);
      const tournament = await Tournament.findByPk(tournamentId);

      if (user && tournament) {
         emailService.sendPaymentFailureEmail(
          payment.toJSON(),
          user,
          tournament,
          error_Message || "Your payment could not be processed"
        );
      } else {
        console.error("Missing data for payment failure email:", {
          userId,
          tournamentId,
        });
      }
    } catch (emailError) {
      console.error("Error sending payment failure email:", emailError);
      // Continue with the response even if email fails
    }

    return res.redirect(
      `${config.frontend_url}/payment-failure?txnid=${txnid}&error=${error_Message}`
    );
  } catch (error) {
    console.error("Error in paymentFailure:", error);
    return res.redirect(
      `${config.frontend_url}/payment-failure?error=server_error`
    );
  }
};

/**
 * Get payment status
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getPaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user.userId;

    if (!paymentId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payment ID is required",
      });
    }

    const payment = await Payment.findOne({
      where: { id: paymentId },
      attributes: { exclude: ["paymentResponse", "createdAt", "updatedAt"] },
    });

    if (!payment) {
      return sendResponse(res, 404, {
        success: false,
        error: "Payment not found",
      });
    }

    // Only allow the player who made the payment or admin to view payment details
    if (payment.userId !== userId && req.user.role !== "admin") {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: payment,
    });
  } catch (error) {
    console.error("Error in getPaymentStatus:", error);
    handleError(res, error);
  }
};

/**
 * Get all payments for a user
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getUserPaymentSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  status: z.enum(["all", "paid", "failed", "pending"]).optional(),
  tournamentTitle: z.string().optional(),
  TransactionId: z.string().optional(),
});
const getUserPayments = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, errors } = getUserPaymentSchema.safeParse(req.query);

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: `"Invalid query parameters" ${errors}`,
      });
    }
    const { page, limit, status, tournamentTitle, TransactionId } = data;
    const whareQurey = {};
    if (status && status !== "all") whareQurey.paymentStatus = status;
    if (TransactionId) whareQurey.paymentTransactionId = TransactionId;

    const { rows: payments, count: total } = await Payment.findAndCountAll({
      where: { userId: userId, ...whareQurey },
      attributes: [
        "id",
        "paymentTransactionId",
        "paymentAmount",
        "paymentCurrency",
        "paymentStatus",

        "paymentRemarks",

        [sequelize.json("payment_response.mode"), "paymentMode"],
        [sequelize.json("payment_response.addedon"), "paymentDate"],
      ],
      offset: (page - 1) * limit,
      limit,
      include: [
        {
          model: Tournament,
          where: tournamentTitle
            ? { title: { [Op.iLike]: `%${tournamentTitle}%` } }
            : {},
          as: "tournament",
          attributes: ["title"],
        },
      ],
      order: [["paymentDate", "DESC"]],
    });

    if (total === 0) {
      sendResponse(res, 204, {
        success: true,
        data: {
          payments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        payments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error in getUserPayments:", error);
    handleError(res, error);
  }
};
const clubPaymentSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  tournamentTitle: z.string().optional(),
  transactionId: z.string().optional(),
  paymentType: z.enum(["player", "club", "all"]).optional().default("all"),
});

const getClubEarnings = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, errors } = clubPaymentSchema.safeParse(req.query);
    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: `Invalid query parameters: ${errors}`,
      });
    }
    const { page, limit, tournamentTitle, transactionId, paymentType } = data;
    const whereQuery = { paymentStatus: "paid" };
    if (transactionId) whereQuery.paymentTransactionId = transactionId;
    // Handle different payment types if specified
    if (paymentType === "player") {
      whereQuery.paymentType = "player";
    } else if (paymentType === "club") {
      whereQuery.paymentType = "club";
    }
    // If no paymentType specified, get both

    // Create a more efficient query by joining first
    const { rows: payments, count: total } = await Payment.findAndCountAll({
      where: whereQuery,
      attributes: [
        "id",
        "paymentTransactionId",
        "paymentAmount",
        "paymentCurrency",
        "paymentStatus",
        "paymentRemarks",
        "userId",
        "paymentType",
        "payment_response",
        [sequelize.json("payment_response.mode"), "paymentMode"],
        [sequelize.json("payment_response.addedon"), "paymentDate"],
        [sequelize.json("payment_response.firstname"), "playerName"],
        [sequelize.json("payment_response.productinfo"), "paymentInfo"],
        [sequelize.json("payment_response.udf5"), "paymentDescription"],
      ],
      offset: (page - 1) * limit,
      limit,
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["title", "clubId"],
          where: tournamentTitle ? { title: tournamentTitle } : {},
          required: tournamentTitle ? true : false,
        },
        {
          model: User,
          as: "user",
          attributes: ["cbid"],
          include: [
            {
              model: ClubDetail,
              attributes: ["clubId", "clubName"],
              required: false,
            },
          ],
        },
        {
          model: BulkRegistration,
          as: "bulkRegistration",
          attributes: ["playersCount", "totalAmount"],
          required: false,
        },
      ],
      order: [["paymentDate", "DESC"]],
    });
    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          payments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    const filteredPayments = payments.filter(
      (payment) => payment.tournament?.clubId === userId
    );

    // Transform the data to include clear payment type info
    const formattedPayments = filteredPayments.map((payment) => {
      const paymentResponse = payment.get("payment_response");
      const basePayment = {
        id: payment.id,
        paymentTransactionId: payment.paymentTransactionId,
        paymentAmount: payment.paymentAmount,
        paymentCurrency: payment.paymentCurrency,
        paymentStatus: payment.paymentStatus,
        paymentMode: payment.paymentMode,
        paymentDate: payment.paymentDate,
        paymentRemarks: payment.paymentRemarks,
        tournamentTitle:
          payment.tournament?.title ||
          payment.paymentInfo?.replace("Registration for ", "") ||
          "",
        paymentType: payment.paymentType || "player",
        cbid: payment.user?.cbid || null,
      };

      // Add specific details based on payment type
      if (payment.paymentType === "club") {
        return {
          ...basePayment,
          playersCount:
            payment.bulkRegistration?.playersCount ||
            parseInt(payment.paymentDescription?.match(/\d+/)?.[0] || "0"),
          totalAmount:
            payment.bulkRegistration?.totalAmount || payment.paymentAmount,
          registrationType: "Bulk Registration",
          clubId: payment.user?.ClubDetail?.clubId || null,
          clubName:
            payment.user?.ClubDetail?.clubName ||
            payment.playerName ||
            "Unknown Club",
        };
      } else {
        return {
          ...basePayment,
          playerName: paymentResponse.firstname || null,
          registrationType: "Player Registration",
        };
      }
    });

    return sendResponse(res, 200, {
      success: true,
      data: {
        payments: formattedPayments,
        total: filteredPayments.length,
        currentPage: page,
        totalPages: Math.ceil(filteredPayments.length / limit),
      },
    });
  } catch (error) {
    console.error("Error in getClubPayments:", error);
    handleError(res, error);
  }
};

const initiateBulkPayment = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { bulkRegistrationId } = req.body;
    if (!userId || !bulkRegistrationId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Missing required parameters",
      });
    }
    const bulkRegistration = await BulkRegistration.findOne({
      where: { id: bulkRegistrationId },
    });
    if (!bulkRegistration) {
      return sendResponse(res, 404, {
        success: false,
        error: "Bulk registration not found",
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    const tournament = await Tournament.findByPk(bulkRegistration.tournamentId);
    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }
    if (bulkRegistration.registrationStatus !== "pending") {
      return sendResponse(res, 400, {
        success: false,
        error: "Registration is not pending",
      });
    }
    if (bulkRegistration.playerList.length === 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "No players found in bulk registration",
      });
    }
    const amount = (
      parseFloat(tournament.entryFee) * bulkRegistration.playerList?.length
    ).toFixed(2);
    if (isNaN(amount)) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid tournament entry fee",
      });
    }
    const productinfo = `Registration for ${tournament.title}`;
    const firstname = user.name || "Bulk Registration";
    const email = user.email;
    const phone = user.phoneNumber;
    const txnid = `CB-${uuidv4().substring(0, 8)}`;
    const payment = await Payment.create({
      userId: userId,
      tournamentId: bulkRegistration.tournamentId,
      paymentTransactionId: txnid,
      paymentAmount: amount,
      bulkRegistrationId: bulkRegistration.id,
      paymentType: "club",
      paymentCurrency: "INR",
      paymentStatus: PAYMENT_STATUS.PENDING,
      paymentMethod: "PayU",
      paymentRemarks: `Payment for ${tournament.title}`,
    });
    const baseUrl = config.backend_url;
    const surl = `${baseUrl}/api/v1/payment/bulk-payment-success`;
    const furl = `${baseUrl}/api/v1/payment/bulk-payment-failure`;
    const paymentData = {
      amount,
      productinfo,
      firstname,
      email,
      phone,
      txnid,
      surl,
      furl,
      udf1: userId,
      udf2: bulkRegistration.tournamentId,
      udf3: payment.id,
      udf4: tournament.title,
      udf5: "Bulk Registration",
    };
    const formData = generatePaymentFormData(paymentData);
    return sendResponse(res, 200, {
      success: true,
      message: "Payment initiated",
      data: {
        paymentId: payment.id,
        formData,
        paymentUrl: config.payu_payment_url,
      },
    });
  } catch (error) {
    console.error("Error in initiateBulkPayment:", error);
    handleError(res, error);
  }
};

const bulkPaymentSuccess = async (req, res) => {
  let transaction = null;

  try {
    const paymentResponse = req.body;

    // Validate payment response
    if (!verifyPaymentResponse(paymentResponse)) {
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=Invalid_hash`
      );
    }

    const {
      status,
      amount,
      txnid,
      mihpayid,
      mode,
      bank_ref_num,
      udf1: userId,
      udf2: tournamentId,
      udf3: paymentId,
      udf4: tournamentTitle,
      udf5,
    } = paymentResponse;

    // Validate payment status
    if (status !== "success") {
      return res.redirect(
        `${config.frontend_url}/payment-failure?txnid=${txnid}`
      );
    }

    // Validate payment type
    if (udf5 !== "Bulk Registration") {
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=Invalid_payment_type`
      );
    }

    // Start transaction
    transaction = await sequelize.transaction();

    try {
      // Find and validate payment
      const payment = await Payment.findByPk(paymentId, { transaction });
      if (!payment) {
        await transaction.rollback();
        return res.redirect(
          `${config.frontend_url}/payment-failure?error=Payment_not_found`
        );
      }

      if (payment.paymentStatus !== PAYMENT_STATUS.PENDING) {
        await transaction.rollback();
        return res.redirect(
          `${config.frontend_url}/payment-failure?error=Payment_already_processed`
        );
      }

      // Update payment status
      await payment.update(
        {
          paymentStatus:
            status === "success" ? PAYMENT_STATUS.PAID : PAYMENT_STATUS.FAILED,
          paymentMethod: mode,
          paymentReference: mihpayid,
          paymentRemarks: bank_ref_num,
          paymentResponse,
        },
        { transaction }
      );

      // Find and validate tournament
      const tournament = await Tournament.findByPk(tournamentId, {
        include: [
          {
            model: User,
            as: "club",
            attributes: ["name", "phoneNumber"],
          },
        ],
        transaction,
      });

      if (!tournament) {
        await transaction.rollback();
        return res.redirect(
          `${config.frontend_url}/payment-failure?error=Tournament_not_found`
        );
      }

      // Only process further for successful payments
      if (status === "success") {
        // Process player registrations
        const bulkRegistration = await BulkRegistration.findByPk(
          payment.bulkRegistrationId,
          { transaction }
        );

        if (bulkRegistration) {
          // Update bulk registration status
          await bulkRegistration.update(
            { registrationStatus: "registered" },
            { transaction }
          );

          // Count existing registrations for this tournament
          const registrationCount = await Registration.count({
            where: { tournamentId },
            transaction,
          });

          const tournamentName = tournament.title;
          const clubName = tournament.club.name;
          const tournamentDate =
            tournament.startDate || tournament.createdAt || new Date();
          const players = bulkRegistration.playerList;

          // Create registrations array
          const newRegistrations = players.map((player, index) => ({
            playerId: player.playerId,
            genderCategory: player.genderCategory,
            ageCategory: player.ageCategory,
            tournamentTitle,
            paymentDate: new Date(),
            tournamentId: bulkRegistration.tournamentId,
            paymentId: payment.id,
            paymentAmount: tournament.entryFee,
            regId: generateRegistrationId(
              tournamentName,
              clubName,
              tournamentDate,
              registrationCount + index
            ),
          }));

          // Bulk create all registrations
          await Registration.bulkCreate(newRegistrations, { transaction });
        }

        // Commit transaction
        await transaction.commit();
        transaction = null;

        // Send notifications (non-critical, outside transaction)
        try {
          // Create notifications for all players
          await notificationService.createBulkRegistrationNotifications(
            payment.bulkRegistrationId,
            userId
          );
          // Process notifications immediately
          await cronService.runJobNow("process-sms-notifications");
        } catch (notificationError) {
          console.error(
            "Error sending notifications (non-critical):",
            notificationError
          );
          // Continue with response even if notification sending fails
        }

        // Redirect to success page
        return res.redirect(
          `${config.frontend_url}/payment-success?txnid=${txnid}&amount=${amount}&tournament=${tournament.title}`
        );
      } else {
        // For non-success status
        await transaction.commit();
        transaction = null;
        return res.redirect(
          `${config.frontend_url}/payment-failure?txnid=${txnid}`
        );
      }
    } catch (error) {
      // Rollback on error
      if (transaction) await transaction.rollback();
      console.error("Error in transaction:", error);
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=Server_error`
      );
    }
  } catch (error) {
    // Rollback if transaction exists
    if (transaction) await transaction.rollback();
    console.error("Error in bulkPaymentSuccess:", error);
    return res.redirect(
      `${config.frontend_url}/payment-failure?error=Server_error`
    );
  }
};

const bulkPaymentFailure = async (req, res) => {
  try {
    const paymentResponse = req.body;

    const isValid = verifyPaymentResponse(paymentResponse);
    if (!isValid) {
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=Invalid_hash`
      );
    }
    const {
      status,
      amount,
      txnid,
      mihpayid,
      mode,
      bank_ref_num,
      udf1: userId,
      udf2: tournamentId,
      udf3: paymentId,
      udf4: tournamentTitle,
      udf5,
    } = paymentResponse;
    const payment = await Payment.findByPk(paymentId);
    if (!payment) {
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=Payment_not_found`
      );
    }
    if (payment.paymentStatus !== PAYMENT_STATUS.PENDING) {
      return res.redirect(
        `${config.frontend_url}/payment-failure?error=Payment_already_processed`
      );
    }
    await payment.update({
      paymentStatus:
        status === "success" ? PAYMENT_STATUS.PAID : PAYMENT_STATUS.FAILED,
      paymentMethod: mode,
      paymentReference: mihpayid,
      paymentRemarks: bank_ref_num,
      paymentResponse: paymentResponse,
    });
    return res.redirect(
      `${config.frontend_url}/payment-failure?txnid=${txnid}`
    );
  } catch (error) {
    console.error("Error in bulkPaymentFailure:", error);
    return res.redirect(
      `${config.frontend_url}/payment-failure?error=Server_error`
    );
  }
};

// Schema for club payments query parameters
const clubPaymentsSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  status: z.enum(["all", "paid", "pending", "failed"]).default("all"),
  tournamentTitle: z.string().optional(),
  transactionId: z.string().optional(),
});

/**
 * Get club bulk payment registrations with player details
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getClubPayments = async (req, res) => {
  try {
    const userId = req.user.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    // Validate query parameters
    const { data, success, error } = clubPaymentsSchema.safeParse(req.query);

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid query parameters",
        details: error.format(),
      });
    }

    // Use validated parameters
    const { page, limit, status, tournamentTitle, transactionId } = data;

    // Find the club associated with this user
    const club = await ClubDetail.findOne({
      where: { userId },
      attributes: ["id", "clubName"],
    });

    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    const payementWhereClause = {};
    if (status !== "all") {
      payementWhereClause.paymentStatus = status;
    }
    if (transactionId) {
      payementWhereClause.paymentTransactionId = transactionId;
    }

    // Build the where clause for BulkRegistration
    const whereClause = {
      registeredBy: club.id,
      registrationStatus: "registered",
    };
    // Find bulk registrations with pagination
    const { rows: bulkRegistrations, count: total } =
      await BulkRegistration.findAndCountAll({
        where: whereClause,
        attributes: [
          "id",
          "bulkRegistrationId",
          "registrationStatus",
          "totalAmount",
          "playersCount",
          "createdAt",
        ],
        offset: (page - 1) * limit,
        limit: parseInt(limit),
        include: [
          {
            model: Tournament,
            as: "tournament",
            where: tournamentTitle
              ? { title: { [Op.iLike]: `%${tournamentTitle}%` } }
              : {},
            attributes: ["id", "title", "startDate", "endDate", "entryFee"],
          },
          {
            model: Payment,
            as: "payment",
            where: payementWhereClause,
            attributes: [
              "id",
              "paymentTransactionId",
              "paymentAmount",
              "paymentStatus",
              "paymentMethod",
              "paymentDate",
              [sequelize.json("payment_response.mode"), "paymentMode"],
              [sequelize.json("payment_response.addedon"), "paymentDate"],
            ],
          },
        ],
        order: [["createdAt", "DESC"]],
      });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          bulkPayments: [],
          total: 0,
          currentPage: parseInt(page),
          totalPages: 0,
        },
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: {
        bulkRegistrations,
        total,
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Error in getClubPayments:", error);
    handleError(res, error);
  }
};

// Schema for pending bulk registrations query parameters
const pendingBulkRegistrationsSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  tournamentId: z.string().uuid().optional(),
});

/**
 * Get pending bulk registrations for a club that haven't been paid for yet
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getPendingBulkRegistrations = async (req, res) => {
  try {
    const userId = req.user.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    // Validate query parameters
    const { data, success, error } = pendingBulkRegistrationsSchema.safeParse(
      req.query
    );

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid query parameters",
        details: error.format(),
      });
    }

    // Use validated parameters
    const { page, limit, tournamentId } = data;

    // Find the club associated with this user
    const club = await ClubDetail.findOne({
      where: { userId },
      attributes: ["id", "clubName"],
    });

    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }

    // Build the where clause for BulkRegistration - only pending registrations
    const whereClause = {
      registeredBy: club.id,
      registrationStatus: "pending",
    };

    // Filter by tournament if specified
    if (tournamentId) {
      whereClause.tournamentId = tournamentId;
    }

    // Find pending bulk registrations with pagination
    const { rows: pendingRegistrations, count: total } =
      await BulkRegistration.findAndCountAll({
        where: whereClause,
        attributes: [
          "id",
          "bulkRegistrationId",
          "registrationStatus",
          "totalAmount",
          "playersCount",
          "createdAt",
          "playerList",
        ],
        offset: (page - 1) * limit,
        limit: parseInt(limit),
        include: [
          {
            model: Tournament,
            as: "tournament",
            attributes: [
              "id",
              "title",
              "startDate",
              "endDate",
              "entryFee",
              "registrationDeadline",
            ],
          },
        ],
        order: [["createdAt", "DESC"]],
      });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          pendingRegistrations: [],
          total: 0,
          currentPage: parseInt(page),
          totalPages: 0,
        },
      });
    }

    // Format the response data
    const formattedRegistrations = await Promise.all(
      pendingRegistrations.map(async (registration) => {
        // Get player details for this bulk registration
        const playerIds = registration.playerList.map((p) => p.playerId);

        // Fetch player details
        const players = await User.findAll({
          where: { id: playerIds },
          attributes: ["id", "name", "email", "cbid"],
          include: [
            {
              model: PlayerDetail,
              attributes: [
                "playerTitle",
                "fideRating",
                "fideId",
                "aicfId",
                "gender",
                "dob",
              ],
            },
          ],
        });

        // Enrich player list with user details
        const enrichedPlayerList = registration.playerList.map((player) => {
          const user = players.find((u) => u.id === player.playerId);
          if (!user) return player;

          return {
            ...player,
            cbid: user.cbid,
            email: user.email,
            playerName: user.name || "",
            playerTitle: user.PlayerDetail?.playerTitle || "",
            fideRating: user.PlayerDetail?.fideRating || "",
            fideId: user.PlayerDetail?.fideId || "",
            aicfId: user.PlayerDetail?.aicfId || "",
            gender: user.PlayerDetail?.gender || "",
            dob: user.PlayerDetail?.dob || "",
          };
        });

        // Format the registration data
        return {
          id: registration.id,
          bulkRegistrationId: registration.bulkRegistrationId,
          registrationStatus: registration.registrationStatus,
          totalAmount: registration.totalAmount,
          playersCount: registration.playersCount,
          createdAt: registration.createdAt,
          tournament: registration.tournament
            ? {
                id: registration.tournament.id,
                title: registration.tournament.title,
                startDate: registration.tournament.startDate,
                endDate: registration.tournament.endDate,
                entryFee: registration.tournament.entryFee,
                registrationDeadline:
                  registration.tournament.registrationDeadline,
              }
            : null,
          players: enrichedPlayerList,
        };
      })
    );

    return sendResponse(res, 200, {
      success: true,
      data: {
        pendingRegistrations: formattedRegistrations,
        total,
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Error in getPendingBulkRegistrations:", error);
    handleError(res, error);
  }
};
const getPaymentReceipt = async (req, res) => {
  try {
    const { txnId } = req.query;

    console.log("Receipt request received for transaction:", txnId);

    if (!txnId) {
      console.log("Missing transaction ID");
      return res.status(400).json({
        success: false,
        error: "Transaction ID is required",
      });
    }

    const raw = await Payment.findOne({
      where: { paymentTransactionId: txnId },
      attributes: [
        "paymentAmount",
        "paymentMethod",
        "paymentTransactionId",
        "paymentDate",
        "paymentStatus",
      ],
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: [
            "title",
            "startDate",
            "country",
            "venueAddress",
            "contactPersonName",
            "contactNumber",
            "email",
          ],
        },
        {
          model: User,
          as: "user",
          attributes: ["name", "phoneNumber", "email", "cbid"],
        },
        {
          model: Registration,
          as: "registration",
          attributes: ["regId"],
        },
      ],
      raw: true,
      nest: true,
    });

    if (!raw) {
      console.log("No payment found for transaction:", txnId);
      return res.status(404).json({
        success: false,
        error: "Payment not found",
      });
    }

    console.log("Payment found:", raw.paymentTransactionId);

    // Format the data for the PDF
    const response = {
      paymentAmount: raw.paymentAmount ? `₹${raw.paymentAmount}` : "₹0.00",
      paymentMethod: raw.paymentMethod || "N/A",
      paymentStatus: raw.paymentStatus || "N/A",
      paymentTransactionId: raw.paymentTransactionId || txnId,
      paymentDate: raw.paymentDate
        ? new Date(raw.paymentDate).toISOString().split("T")[0]
        : "N/A",
      tournamentTitle: raw.tournament?.title || "N/A",
      registrationId: raw.registration?.regId || "N/A",
      tournamentStartDate: raw.tournament?.startDate
        ? new Date(raw.tournament.startDate).toISOString().split("T")[0]
        : "N/A",
      tournamentCountry: raw.tournament?.country || "N/A",
      tournamentOrganizerName: raw.tournament?.contactPersonName || "N/A",
      tournamentOrganizerEmail: raw.tournament?.email || "N/A",
      tournamentOrganizerPhoneNumber: raw.tournament?.contactNumber || "N/A",
      tournamentVenueAddress: raw.tournament?.venueAddress || "N/A",
      playerId: raw.user?.cbid || "N/A",
      playerName: raw.user?.name || "N/A",
      playerPhoneNumber: raw.user?.phoneNumber || "N/A",
      playerEmail: raw.user?.email || "N/A",
      timestamp: new Date().toISOString(),
    };

    console.log("Generating PDF with data");
    return sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error processing receipt request:", error);
    return handleError(res, error);
  }
};

module.exports = {
  initiatePayment,
  paymentSuccess,
  paymentFailure,
  getPaymentStatus,
  getUserPayments,
  getClubEarnings,
  initiateBulkPayment,
  bulkPaymentSuccess,
  bulkPaymentFailure,
  getClubPayments,
  getPendingBulkRegistrations,
  getPaymentReceipt,
};

function generateRegistrationId(
  tournamentName,
  clubName,
  tournamentDate,
  currentCount = 0
) {
  // Generate club abbreviation (3 chars)
  const clubAbbr = generateAbbreviation(clubName, 3);

  // Generate tournament abbreviation (3 chars)
  const tournamentAbbr = generateAbbreviation(tournamentName, 3);

  // Extract year and month from tournament date
  const date = new Date(tournamentDate);
  const year = date.getFullYear().toString().slice(2); // Get last 2 digits of year
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Month is 0-indexed

  // Format counter with leading zeros (4 digits)
  const paddedCount = String(currentCount + 1).padStart(4, "0");

  // Combine into the final format with date info
  return `${clubAbbr}-${tournamentAbbr}-${month}${year}-${paddedCount}`;
}

/**
 * Generate an abbreviation from a name with specified length
 *
 * @param name The name to abbreviate
 * @param length The desired abbreviation length
 * @returns An abbreviation in lowercase
 */
function generateAbbreviation(name, length) {
  // Normalize the name: convert to lowercase and remove special characters
  const normalized = name.toLowerCase().replace(/[^\w\s]/g, "");

  // Split into words
  const words = normalized.split(/\s+/).filter((w) => w.length > 0);

  // If we have enough words, use first letter of each word
  if (words.length >= length) {
    return words
      .slice(0, length)
      .map((w) => w[0])
      .join("");
  }

  // If we don't have enough words, use first letters plus more letters from first word
  let abbr = words.map((w) => w[0]).join("");

  // If still not enough, add more characters from the first word
  if (abbr.length < length && words.length > 0) {
    const firstWord = words[0];
    abbr += firstWord.substring(1, length - abbr.length + 1);
  }

  // Pad with 'x' if necessary (should rarely happen)
  while (abbr.length < length) {
    abbr += "x";
  }

  return abbr;
}
