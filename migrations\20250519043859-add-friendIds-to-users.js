'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('players', 'friendIds', {
      type: Sequelize.ARRAY(Sequelize.UUID),
      allowNull: false,
      defaultValue: [],
      field: 'friend_ids',  // optional: if you want snake_case in DB
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('players', 'friendIds');
  },
};
