const router = require("express").Router();

const {
  createTournament,
  editTournament,
  getAllTournament,
  getSingleTournament,
  getUpcomingTournament,
  deleteTournament,
  getClubTournaments,
  downlaodBrochure,
} = require("../controllers/tournamentController");
const verifyJwt = require("../middlewares/verifyJwt");
const registrationRouter = require("./register");
const tournamentRouter = require("express").Router();
const { uploadFactory, handleUploadError } = require("../utils/s3");
const parseJSONFields = (req, res, next) => {
  const fieldsToParse = [
    "maleAgeCategory",
    "femaleAgeCategory",
    "foodFacility",
    "chiefArbiterName",
  ];
  fieldsToParse.forEach((field) => {
    if (req.body[field]) {
      try {
        req.body[field] = JSON.parse(req.body[field]);
      } catch (e) {
        // leave it as is if parsing fails
      }
    }
  });
  next();
};

router.use("/:title/register", registrationRouter);

router.use("/club", tournamentRouter);
router.post(
  "/",
  verifyJwt,
  uploadFactory.tournament.brochure(),
  handleUploadError,
  parseJSONFields,
  createTournament
);
router.put(
  "/:id",
  verifyJwt,
  uploadFactory.tournament.brochure(),
  handleUploadError,
  parseJSONFields,
  editTournament
);
router.get("/", getAllTournament);
router.get("/:id", getSingleTournament);
router.get("/:id/brochure", downlaodBrochure);
router.delete("/:id", verifyJwt, deleteTournament);

tournamentRouter.get("/upcoming", verifyJwt, getUpcomingTournament);
tournamentRouter.get("/", verifyJwt, getClubTournaments);

module.exports = router;
