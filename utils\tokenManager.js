const { config, database } = require("../config/config");

// Simple in-memory cache

let tokenData = {
  accessToken: null,
  expiresAt: null, // timestamp in ms
};

async function getPayUToken() {
  const now = Date.now();

  // If token exists and is valid
  if (tokenData.accessToken && now < tokenData.expiresAt) {
    return tokenData.accessToken;
  }

  // Otherwise, get a new token
  const response = await fetch("https://uat-accounts.payu.in/oauth/token", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      client_id: config.payu_client_id,
      client_secret: config.payu_client_secret,
      grant_type: "client_credentials",
      scope: "verify_bank_account",
    }),
  });

  const data = await response.json();
  console.log(data)

  // Cache the token
  tokenData.accessToken = data.access_token;
  tokenData.expiresAt = Date.now() + data.expires_in * 1000;

  return tokenData.accessToken;
}

module.exports = { getPayUToken };
