const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Otp extends Model {
    static associate(models) {
     
    }
  }
  
  Otp.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: true,
        field: "user_id",
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      phoneNumber: {
        type: DataTypes.STRING(15),
        allowNull: true,
        field: "phone_number",
      },
      otp: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM("password-reset", "registration", "verification"),
        allowNull: false,
      },
      platform: {
        type: DataTypes.ENUM("sms", "email", "whatsapp"),
        allowNull: false,
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: "expires_at",
      },
    },
    {
      sequelize,
      modelName: "Otp",
      tableName: "otp",
      timestamps: true,
    }
  );

  return Otp;
};
