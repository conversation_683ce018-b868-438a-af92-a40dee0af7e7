const router = require("express").Router();
const { getContent, updateContent, createContent, } = require("../../controllers/admin/index");
const {
  // getEmailTemplates,
  getSmsTemplates,
  getWhatsappTemplates,
  sendBulkEmail,
  sendBulkSms,
  sendBulkWhatsapp
} = require("../../controllers/admin/communicationController");
const { getAllClubDetails, getAllArbiter, getAllPlayer } = require("../../controllers/admin/userController");
const verifyJwt = require("../../middlewares/verifyJwt")

// Content routes
router.get("/content/:slug", verifyJwt, getContent);
router.post("/content", verifyJwt, createContent);
router.put("/content/:slug", verifyJwt, updateContent);

// Template routes
// router.get("/email-templates", verifyJwt, getEmailTemplates);
router.get("/sms-templates", verifyJwt, getSmsTemplates);
router.get("/whatsapp-templates", verifyJwt, getWhatsappTemplates);

// Bulk messaging routes
router.post("/send-email", verifyJwt, sendBulkEmail);
router.post("/send-sms", verifyJwt, sendBulkSms);
router.post("/send-whatsapp", verifyJwt, sendBulkWhatsapp);

// Search routes
router.get("/users/player", verifyJwt, getAllPlayer);
router.get("/users/club", verifyJwt, getAllClubDetails);
router.get("/users/arbiter", verifyJwt, getAllArbiter);

module.exports = router;
