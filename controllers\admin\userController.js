const { Op, literal } = require("sequelize");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const{getAllPlayerSchema,getAllArbiterSchema} = require("../../schema/playerSchama");
const{getAllClubSchema} = require("../../schema/clubSchema");
const {
  ClubDetail,
  ArbiterDetails,
  PlayerDetail,
  User,
} = require("../../config/db").models;

const getAllClubDetails = async (req, res) => {
  const { data, success, error } = getAllClubSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      clubName = "",
      country = "",
      state = "",
      district = "",
      city = "",
    } = data;

    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { clubName, country, state, district, city };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });

    const { rows: clubs, count } = await ClubDetail.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: { exclude: ["id", "createdAt", "updatedAt"] },
      include: [
        {
          model: User,
          as: "user",
          attributes: ["email", "phoneNumber"],
          required: false,
        },
      ],
      order: [["clubName", "ASC"]],
    });

    const response = {
      clubs,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getAllArbiter = async (req, res) => {
  const { data, success, error } = getAllArbiterSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      arbiterId,
      arbiterName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (arbiterId && !arbiterId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${arbiterId}%` } },
        { aicfId: { [Op.iLike]: `%${arbiterId}%` } },
        { stateId: { [Op.iLike]: `%${arbiterId}%` } },
        { districtId: { [Op.iLike]: `%${arbiterId}%` } },
        { officialId: { [Op.iLike]: `%${arbiterId}%` } },
      ];
    }
    let userWhereClause = {};
    if (arbiterId && arbiterId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${arbiterId}%` } };
    }
    if (arbiterName) userWhereClause.name = { [Op.iLike]: `%${arbiterName}%` };
    const { rows: arbiters, count } = await ArbiterDetails.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "title",

        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "profileUrl",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: User,
          where: userWhereClause,
          as: "user",
          attributes: ["cbid", "name", "email", "phoneNumber"],
          required: true,
        },
      ],
    });

    const formattedArbiters = arbiters.map((arbiter) => {
      const arbiterData = arbiter.toJSON();
      if (arbiter.user) {
        return { ...arbiterData, ...arbiter.user.toJSON() };
      }
      return arbiterData;
    });
    const response = {
      arbiters: formattedArbiters,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getAllPlayer = async (req, res) => {
  const { data, success, error } = getAllPlayerSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      playerId,
      playerName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }
    let userWhereClause = {};
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };
    const { rows: players, count } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "playerTitle",
        "fideRating",
        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: User,
          where: userWhereClause,
          attributes: ["cbid", "name", "email", "phoneNumber"],
          required: true,
        },
      ],
      order: [["fideRating", "DESC"]],
    });
    const formattedPlayers = players.map((player) => {
      const playerData = player.toJSON();
      if (player.User) {
        return { ...playerData, ...player.User.toJSON() };
      }
      return playerData;
    });
    const response = {
      players: formattedPlayers,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};


module.exports = {
  getAllClubDetails,
  getAllArbiter,
  getAllPlayer,
};
