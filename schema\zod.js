const z = require("zod");

const createTournamentSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(255, "Title must be at most 255 characters"),

  fideRated: z.boolean().default(false),

  organizerName: z.string().min(1, "Organizer name is required"),

  tournamentLevel: z.enum(
    [
      "international",
      "national",
      "state",
      "district",
      "club",
      "school",
      "college",
      "university",
      "other",
    ],
    { message: "Invalid tournament level" }
  ),

  startDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  reportingTime: z
    .string()
    .regex(/^\d{2}:\d{2}(:\d{2})?$/, "Invalid time format (HH:MM or HH:MM:SS)"),

  registrationDeadline: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
  registrationDeadlineTime: z
    .string()
    .regex(/^\d{2}:\d{2}(:\d{2})?$/, "Invalid time format (HH:MM or HH:MM:SS)"),

  chiefArbiterId: z.string().uuid("Invalid UUID for Chief Arbiter ID"),
  tournamentDirectorName: z
    .string()
    .min(1, "Tournament director name is required"),

  entryFeeCurrency: z.enum(["INR", "USD", "EUR"], {
    message: "Invalid currency",
  }),
  entryFee: z.number().min(0, "Registration fee cannot be negative"),

  numberOfRounds: z
    .number()
    .int()
    .min(1, "Number of rounds must be at least 1"),

  timeControlType: z.string().min(1, "Time control type is required"),
  timeControlDuration: z.string().min(1, "Time control duration is required"),
  timeControlIncrement: z.string().min(1, "Time control increment is required"),

  tournamentType: z
    .enum(["rapid", "blitz", "classical", "bullet", "other"], {
      message: "Invalid tournament type",
    })
    .default("classical"),

  nationalApproval: z.string().min(1, "National approval is required"),
  stateApproval: z.string().min(1, "State approval is required"),
  districtApproval: z.string().min(1, "District approval is required"),

  contactPersonName: z.string().min(1, "Contact person name is required"),
  emailId: z.string().email("Invalid email format"),
  contactNumber: z.string().min(10, "Invalid contact number"),
  alternateContact: z.string().optional(),

  numberOfTrophiesMale: z.number().int().min(0, "Trophies must be at least 0"),
  numberOfTrophiesFemale: z
    .number()
    .int()
    .min(0, "Trophies must be at least 0"),

    totalCashPrizeCurrency: z.enum(["INR", "USD", "EUR"], {
      message: "Invalid currency",
    }),
    totalCashPrizeAmount: z.number().min(0, "Cash prize cannot be negative"),

  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  district: z.string().min(1, "District is required"),
  city: z.string().min(1, "City is required"),
  pincode: z.string().min(1, "Pincode is required"),

  venueAddress: z.string().min(1, "Venue address is required"),
  nearestLandmark: z.string().min(1, "Nearest landmark is required"),

  brochureUrl: z.string().url("Invalid brochure URL format"),

  locationUrl: z.string().url("Invalid location URL format"),

  chessboardProvided: z.boolean(),
  timerProvided: z.boolean(),

  parkingFacility: z.enum(["yes", "no", "limited"], {
    message: "Invalid parking option",
  }),

  foodFacility: z.enum(
    ["breakfast", "lunch", "dinner", "snacks", "beverages", "nil"],
    { message: "Invalid food option" }
  ),

  maleAgeCategories: z
    .string()
    .min(1, "Male age categories is required")
    .default("open"),
  femaleAgeCategories: z
    .string()
    .min(1, "Female age categories is required")
    .default("open"),
});

module.exports = { createTournamentSchema };
