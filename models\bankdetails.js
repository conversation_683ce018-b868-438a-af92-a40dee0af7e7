const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class BankDetails extends Model {
    static associate(models) {
      BankDetails.belongsTo(models.User, {
        foreignKey: "clubId",
        onDelete: "CASCADE",
        as: "user",
        scope: {
          role: "club",
        },
      });
    }
  }

  BankDetails.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      clubId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: 'club_id',
      },
      bankName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'bank_name',
      },
      AccountNumber: {
        type: DataTypes.STRING(50),
        allowNull: false,
          field: 'account_number',
      },
      branchIFSCCode: {
        type: DataTypes.STRING(20),
        allowNull: false,
        field: 'branch_ifsc_code',
      },
      branchName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'branch_name',
      },
      bankAccountType: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: 'bank_account_type',
      },
      bankAccountHolderName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'bank_account_holder_name',
      },
      isLocked: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        field: 'is_locked',
      },
      isVerified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        field: 'is_verified',
      },
    },
    {
      sequelize,
      modelName: "BankDetails",
      tableName: "bank_details",
      timestamps: true,
    }
  );

  return BankDetails;
};
