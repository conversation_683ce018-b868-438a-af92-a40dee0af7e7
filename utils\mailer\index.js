const nodemailer = require('nodemailer');
const handlebars = require('handlebars');
const fs = require('fs');
const path = require('path');
const { config } = require('../../config/config');

// Create a transporter with the configured settings
const transporter = nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.auth.user,
    pass: config.email.auth.pass
  }
});

/**
 * Load and compile an email template
 * @param {string} templateName - Name of the template to load
 * @returns {Function} - Compiled template function
 */
const loadTemplate = (templateName) => {
  const templatePath = path.join(__dirname, 'templates', `${templateName}.hbs`);
  
  try {
    const templateSource = fs.readFileSync(templatePath, 'utf8');
    return handlebars.compile(templateSource);
  } catch (error) {
    console.error(`Error loading template ${templateName}:`, error);
    throw new Error(`Template ${templateName} not found or invalid`);
  }
};

/**
 * Register Handlebars helpers
 */
handlebars.registerHelper('formatDate', function(date) {
  if (!date) return '';
  const dateObj = new Date(date);
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

handlebars.registerHelper('formatTime', function(date) {
  if (!date) return '';
  const dateObj = new Date(date);
  return dateObj.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
});

handlebars.registerHelper('formatCurrency', function(amount) {
  if (!amount) return '₹0.00';
  return `₹${parseFloat(amount).toFixed(2)}`;
});

/**
 * Send an email using a template
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email address
 * @param {string} options.subject - Email subject
 * @param {string} options.templateName - Name of the template to use
 * @param {Object} options.templateData - Data to pass to the template
 * @param {Array} [options.attachments] - Optional attachments
 * @returns {Promise<Object>} - Nodemailer send result
 */
const sendEmail = async ({ to, subject, templateName, templateData, attachments = [] }) => {
  try {

    // Validate required fields
    if (!to || !subject || !templateName) {
      throw new Error('Missing required email parameters: to, subject, templateName');
    }

    // Load and compile the template
    const template = loadTemplate(templateName);
    
    // Render the HTML with the provided data
    const html = template(templateData || {});

    // Configure email options
    const mailOptions = {
      from: config.email.from,
      to,
      subject,
      html,
      attachments
    };

    // Send the email
    const info = await transporter.sendMail(mailOptions);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

/**
 * Verify the email configuration
 * @returns {Promise<boolean>} - Whether the configuration is valid
 */
const verifyConfiguration = async () => {
  try {
    await transporter.verify();
    return true;
  } catch (error) {
    console.error('Email configuration verification failed:', error);
    return false;
  }
};

module.exports = {
  sendEmail,
  verifyConfiguration
};
