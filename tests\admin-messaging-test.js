/**
 * Test script for Admin Messaging API endpoints
 * Run with: node tests/admin-messaging-test.js
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api/v1';

// You'll need to replace this with a valid admin JWT token
const ADMIN_TOKEN = 'your_admin_jwt_token_here';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${ADMIN_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testEmailTemplates() {
  console.log('\n🧪 Testing Email Templates...');
  try {
    const response = await api.get('/admin/email-templates');
    console.log('✅ Email templates fetched successfully');
    console.log(`📊 Total templates: ${response.data.data.totalTemplates}`);
    console.log(`📂 Categories: ${Object.keys(response.data.data.templates).join(', ')}`);
  } catch (error) {
    console.error('❌ Error fetching email templates:', error.response?.data || error.message);
  }
}

async function testSmsTemplates() {
  console.log('\n🧪 Testing SMS Templates...');
  try {
    const response = await api.get('/admin/sms-templates');
    console.log('✅ SMS templates fetched successfully');
    console.log(`📊 Total templates: ${response.data.data.totalTemplates}`);
    console.log(`📂 Categories: ${Object.keys(response.data.data.templates).join(', ')}`);
  } catch (error) {
    console.error('❌ Error fetching SMS templates:', error.response?.data || error.message);
  }
}

async function testWhatsappTemplates() {
  console.log('\n🧪 Testing WhatsApp Templates...');
  try {
    const response = await api.get('/admin/whatsapp-templates');
    console.log('✅ WhatsApp templates fetched successfully');
    console.log(`📊 Total templates: ${response.data.data.totalTemplates}`);
    console.log(`📂 Categories: ${Object.keys(response.data.data.templates).join(', ')}`);
  } catch (error) {
    console.error('❌ Error fetching WhatsApp templates:', error.response?.data || error.message);
  }
}

async function testPlayerSearch() {
  console.log('\n🧪 Testing Player Search...');
  try {
    const response = await api.get('/admin/players/search', {
      params: {
        page: 1,
        limit: 5,
        playerName: 'test'
      }
    });
    console.log('✅ Player search successful');
    console.log(`📊 Total players found: ${response.data.data?.total || 0}`);
  } catch (error) {
    console.error('❌ Error searching players:', error.response?.data || error.message);
  }
}

async function testClubSearch() {
  console.log('\n🧪 Testing Club Search...');
  try {
    const response = await api.get('/admin/clubs/search', {
      params: {
        page: 1,
        limit: 5,
        clubName: 'test'
      }
    });
    console.log('✅ Club search successful');
    console.log(`📊 Total clubs found: ${response.data.data?.total || 0}`);
  } catch (error) {
    console.error('❌ Error searching clubs:', error.response?.data || error.message);
  }
}

async function testBulkEmail() {
  console.log('\n🧪 Testing Bulk Email (Dry Run)...');
  try {
    const testPayload = {
      recipients: [
        {
          email: '<EMAIL>',
          name: 'Test User',
          id: 'test123'
        }
      ],
      subject: 'Test Email from Admin Messaging System',
      customContent: '<h1>Hello!</h1><p>This is a test email from the admin messaging system.</p>'
    };

    // Note: This will actually send an email if the service is configured
    // Comment out the next line if you don't want to send a real email
    // const response = await api.post('/admin/send-email', testPayload);
    
    console.log('✅ Bulk email endpoint is available');
    console.log('⚠️  Skipping actual email send to avoid spam');
    // console.log(`📊 Email result: ${response.data.data.successful} sent, ${response.data.data.failed} failed`);
  } catch (error) {
    console.error('❌ Error testing bulk email:', error.response?.data || error.message);
  }
}

async function testBulkSms() {
  console.log('\n🧪 Testing Bulk SMS (Dry Run)...');
  try {
    const testPayload = {
      recipients: [
        {
          mobile: '919876543210',
          name: 'Test User',
          id: 'test123'
        }
      ],
      templateId: 'test_template_id',
      variables: {
        VAR1: 'Test User',
        VAR2: 'Test Message'
      }
    };

    // Note: This will actually send an SMS if the service is configured
    // Comment out the next line if you don't want to send a real SMS
    // const response = await api.post('/admin/send-sms', testPayload);
    
    console.log('✅ Bulk SMS endpoint is available');
    console.log('⚠️  Skipping actual SMS send to avoid charges');
    // console.log(`📊 SMS result: ${response.data.data.successful} sent, ${response.data.data.failed} failed`);
  } catch (error) {
    console.error('❌ Error testing bulk SMS:', error.response?.data || error.message);
  }
}

async function testBulkWhatsapp() {
  console.log('\n🧪 Testing Bulk WhatsApp (Simulation)...');
  try {
    const testPayload = {
      recipients: [
        {
          mobile: '919876543210',
          name: 'Test User',
          id: 'test123'
        }
      ],
      templateId: 'welcome',
      variables: {
        playerName: 'Test User',
        tournamentName: 'Test Tournament'
      }
    };

    const response = await api.post('/admin/send-whatsapp', testPayload);
    console.log('✅ Bulk WhatsApp endpoint working');
    console.log(`📊 WhatsApp result: ${response.data.data.successful} sent, ${response.data.data.failed} failed`);
    if (response.data.data.note) {
      console.log(`📝 Note: ${response.data.data.note}`);
    }
  } catch (error) {
    console.error('❌ Error testing bulk WhatsApp:', error.response?.data || error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Admin Messaging API Tests...');
  console.log(`🔗 API Base URL: ${API_BASE_URL}`);
  
  if (ADMIN_TOKEN === 'your_admin_jwt_token_here') {
    console.log('⚠️  Please update ADMIN_TOKEN in the test file with a valid admin JWT token');
    return;
  }

  await testEmailTemplates();
  await testSmsTemplates();
  await testWhatsappTemplates();
  await testPlayerSearch();
  await testClubSearch();
  await testBulkEmail();
  await testBulkSms();
  await testBulkWhatsapp();

  console.log('\n✨ All tests completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Update ADMIN_TOKEN with a valid admin JWT token');
  console.log('2. Configure MSG91 API keys for SMS functionality');
  console.log('3. Test with real recipients (use test email/phone numbers)');
  console.log('4. Integrate the frontend components into your admin dashboard');
  console.log('5. Set up proper error handling and logging');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testEmailTemplates,
  testSmsTemplates,
  testWhatsappTemplates,
  testPlayerSearch,
  testClubSearch,
  testBulkEmail,
  testBulkSms,
  testBulkWhatsapp
};
