/**
 * Test script for pairing extraction
 *
 * This script tests the pairing extraction functionality with a sample Excel file.
 * It extracts the first five rows of data and displays the results.
 */

const XLSX = require('xlsx');
const path = require('path');
const extractor = require('./scripts/excel-extractor');
require('dotenv').config();

// Sample data for testing
const sampleData = [
  {
    'Bo.': 1,
    'White': 'Player 1',
    'Rtg': 1800,
    'pts': 3.5,
    'Result': '1-0',
    'Black': 'Player 2',
    'Rtg': 1750,
    'pts': 2.0
  },
  {
    'Bo.': 2,
    'White': 'Player 3',
    'Rtg': 2100,
    'pts': 4.0,
    'Result': '0-1',
    'Black': 'Player 4',
    'Rtg': 2200,
    'pts': 4.5
  },
  {
    'Bo.': 3,
    'White': 'Player 5',
    'Rtg': 1950,
    'pts': 3.0,
    'Result': '½-½',
    'Black': 'Player 6',
    'Rtg': 1900,
    'pts': 3.0
  },
  {
    'Bo.': 4,
    'White': 'Player 7',
    'Rtg': 1650,
    'pts': 2.5,
    'Result': '1-0',
    'Black': 'Player 8',
    'Rtg': 1600,
    'pts': 2.0
  },
  {
    'Bo.': 5,
    'White': 'Player 9',
    'Rtg': 2000,
    'pts': 3.5,
    'Result': '0-1',
    'Black': 'Player 10',
    'Rtg': 2050,
    'pts': 4.0
  }
];

// Create a mock worksheet with the sample data
function createMockWorksheet() {
  // Create a new workbook
  const wb = XLSX.utils.book_new();

  // Create a worksheet manually to handle duplicate column names
  const ws = {};
  ws['!ref'] = 'A1:H6'; // A1 to H6 (8 columns, 6 rows including header)

  // Add headers
  ws['A1'] = { t: 's', v: 'Bo.' };
  ws['B1'] = { t: 's', v: 'White' };
  ws['C1'] = { t: 's', v: 'Rtg' };  // First Rtg (white)
  ws['D1'] = { t: 's', v: 'pts' };  // First pts (white)
  ws['E1'] = { t: 's', v: 'Result' };
  ws['F1'] = { t: 's', v: 'Black' };
  ws['G1'] = { t: 's', v: 'Rtg' };  // Second Rtg (black)
  ws['H1'] = { t: 's', v: 'pts' };  // Second pts (black)

  // Add data for row 1
  ws['A2'] = { t: 'n', v: 1 };
  ws['B2'] = { t: 's', v: 'Player 1' };
  ws['C2'] = { t: 'n', v: 1800 };  // White rating
  ws['D2'] = { t: 'n', v: 3.5 };   // White points
  ws['E2'] = { t: 's', v: '1-0' };
  ws['F2'] = { t: 's', v: 'Player 2' };
  ws['G2'] = { t: 'n', v: 1750 };  // Black rating
  ws['H2'] = { t: 'n', v: 2.0 };   // Black points

  // Add data for row 2
  ws['A3'] = { t: 'n', v: 2 };
  ws['B3'] = { t: 's', v: 'Player 3' };
  ws['C3'] = { t: 'n', v: 2100 };  // White rating
  ws['D3'] = { t: 'n', v: 4.0 };   // White points
  ws['E3'] = { t: 's', v: '0-1' };
  ws['F3'] = { t: 's', v: 'Player 4' };
  ws['G3'] = { t: 'n', v: 2200 };  // Black rating
  ws['H3'] = { t: 'n', v: 4.5 };   // Black points

  // Add data for row 3
  ws['A4'] = { t: 'n', v: 3 };
  ws['B4'] = { t: 's', v: 'Player 5' };
  ws['C4'] = { t: 'n', v: 1950 };  // White rating
  ws['D4'] = { t: 'n', v: 3.0 };   // White points
  ws['E4'] = { t: 's', v: '½-½' };
  ws['F4'] = { t: 's', v: 'Player 6' };
  ws['G4'] = { t: 'n', v: 1900 };  // Black rating
  ws['H4'] = { t: 'n', v: 3.0 };   // Black points

  // Add data for row 4
  ws['A5'] = { t: 'n', v: 4 };
  ws['B5'] = { t: 's', v: 'Player 7' };
  ws['C5'] = { t: 'n', v: 1650 };  // White rating
  ws['D5'] = { t: 'n', v: 2.5 };   // White points
  ws['E5'] = { t: 's', v: '1-0' };
  ws['F5'] = { t: 's', v: 'Player 8' };
  ws['G5'] = { t: 'n', v: 1600 };  // Black rating
  ws['H5'] = { t: 'n', v: 2.0 };   // Black points

  // Add data for row 5
  ws['A6'] = { t: 'n', v: 5 };
  ws['B6'] = { t: 's', v: 'Player 9' };
  ws['C6'] = { t: 'n', v: 2000 };  // White rating
  ws['D6'] = { t: 'n', v: 3.5 };   // White points
  ws['E6'] = { t: 's', v: '0-1' };
  ws['F6'] = { t: 's', v: 'Player 10' };
  ws['G6'] = { t: 'n', v: 2050 };  // Black rating
  ws['H6'] = { t: 'n', v: 4.0 };   // Black points

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

  // Save the workbook to a temporary file
  const tempFile = path.join(__dirname, 'temp-pairing-test.xlsx');
  XLSX.writeFile(wb, tempFile);
  return { workbook: wb, worksheet: ws, filePath: tempFile };
}

// Test the pairing extraction
async function testPairingExtraction() {
  try {
    // Create a mock worksheet
    const { worksheet, filePath } = createMockWorksheet();

    // Mock tableInfo object
    const tableInfo = {
      found: true,
      start: 0,  // 0-based index for the header row (row 1 in Excel)
      end: 5,    // 0-based index for the last data row (row 6 in Excel)
      headers: [
        { col: 0, value: 'Bo.' },     // Column A
        { col: 1, value: 'White' },   // Column B
        { col: 2, value: 'Rtg' },     // Column C - First Rtg (white)
        { col: 3, value: 'pts' },     // Column D - First pts (white)
        { col: 4, value: 'Result' },  // Column E
        { col: 5, value: 'Black' },   // Column F
        { col: 6, value: 'Rtg' },     // Column G - Second Rtg (black)
        { col: 7, value: 'pts' }      // Column H - Second pts (black)
      ]
    };

    // Test extractPairingTableData
    const extractedData = extractor.extractPairingTableData(worksheet, tableInfo);
    extractedData.slice(0, 5).forEach((row, i) => {
    });

    // Test mapToPairingModel
    const mappedData = extractor.mapToPairingModel(extractedData, {
      tournament_id: 123,
      round_id: 1,
      category: 'Open'
    });
    mappedData.slice(0, 5).forEach((row, i) => {
    });

    // Test the full process
    const result = await extractor.processExcelFile(filePath, {
      tournament_id: 123,
      round_id: 1,
      category: 'Open',
      modelName: 'Pairing',
      saveToDb: false
    });
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testPairingExtraction().catch(console.error);
