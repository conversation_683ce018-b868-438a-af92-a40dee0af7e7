const express = require("express");
const dotenv = require("dotenv");
const cookieParser = require("cookie-parser");
const helmet = require("helmet");
const router = require("./routes/index");
const { connectDb } = require("./config/db");
const cors = require("cors");
const cronService = require("./services/cronService");

dotenv.config();

const app = express();

const allowedOrigins = [
  "http://************:5173",
  "http://**************:5173",
  "http://**************:3000",
  "http://**********:5173",
  "http://**********:3000",
  "http://localhost:5173",
  "http://localhost:3000",
];

// app.use(
//   cors({
//     origin: (origin, callback) => {
//       if (!origin || allowedOrigins.includes(origin)) {
//         callback(null, true);
//       } else {
//         callback(new Error("Not allowed by CORS"));
//       }
//     },
//     methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
//     allowedHeaders: ["Content-Type", "Authorization"],
//     exposedHeaders: ["Authorization"],
//     credentials: true, // ✅ Allow cookies & auth headers
//   })
// );
app.use(
  cors({
    origin: true,
    credentials: true,
  })
);
app.options("*", cors());
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
connectDb();

// Routes
app.use("/api/v1", router);

app.get("/health", (req, res) => {
  res.status(200).json({ status: "OK" });
});

const PORT = process.env.PORT || 3000;
// app.listen(PORT, () => {
//   console.log(`Server is running on port ${PORT}`);
// });
app.listen(3000, "0.0.0.0", () => {
  console.log(`Server is running on port ${PORT}`);

  // // Initialize cron jobs
  // cronService.initCronJobs();
  // console.log('Cron jobs initialized');
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: "Internal Server Error" });
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  // Application specific logging, throwing an error, or other logic here
});
