const express = require("express");
const router = express.Router();
const {
  initiatePayment,
  paymentSuccess,
  paymentFailure,
  getPaymentStatus,
  getUserPayments,
  getClubEarnings,
  initiateBulkPayment,
  bulkPaymentSuccess,
  bulkPaymentFailure,
  getClubPayments,
  getPendingBulkRegistrations,
  getPaymentReceipt,
} = require("../controllers/paymentController");
const verifyJwt = require("../middlewares/verifyJwt");

// Payment initiation route (requires authentication)
router.post("/initiate", verifyJwt, initiatePayment);
router.post("/bulk-initiate", verifyJwt, initiateBulkPayment);

// Payment callback routes (no authentication required as they are called by PayU)
router.post("/success", paymentSuccess);
router.post("/failure", paymentFailure);
router.post("/bulk-payment-success", bulkPaymentSuccess);
router.post("/bulk-payment-failure", bulkPaymentFailure);

// Payment status routes (requires authentication)
router.get("/status/:paymentId", verifyJwt, getPaymentStatus);
router.get("/user", verifyJwt, getUserPayments);
router.get("/club/tournament", verifyJwt, getClubEarnings);
router.get("/club", verifyJwt, getClubPayments);
router.get("/club/pending-registrations", verifyJwt, getPendingBulkRegistrations);
router.get("/receipt", verifyJwt, getPaymentReceipt);

module.exports = router;
