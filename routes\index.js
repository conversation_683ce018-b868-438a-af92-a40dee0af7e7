const router = require("express").Router();
const authRoutes = require("./auth");
const userRoutes = require("./user");
const tournamentRoutes = require("./tournament");
const presignRoutes = require("./presign");
const playerRoutes = require("./player");
const clubRoutes = require("./club");
const paymentRoutes = require("./payment");
const locationRoutes = require("./location");
const rankingImportRoutes = require("./rankingImportRoutes");
const pairingImportRoutes = require("./pairingImportRoutes");
const arbiterRouter = require("./arbiter");
const notificationRoutes = require("./notification");
const reportRoutes = require("./reportRoutes");
const verifyJwt = require("../middlewares/verifyJwt");
const verifyRole = require("../middlewares/verifyRole");
const adminRoutes = require("./admin/index");
const publicRoutes = require("./public");

router.use("/auth", authRoutes);
router.use("/user", userRoutes);
router.use("/tournament", tournamentRoutes);
router.use("/arbiter", arbiterRouter);
router.use("/player", playerRoutes);
router.use("/club", clubRoutes);
router.use("/presign", presignRoutes);
router.use("/payment", paymentRoutes);
router.use("/location", locationRoutes);
router.use("/ranking-import", rankingImportRoutes);
router.use("/pairing-import", pairingImportRoutes);
router.use("/notification", notificationRoutes);
router.use("/report", reportRoutes);
router.use("/admin", verifyJwt, verifyRole(["admin"]), adminRoutes);
router.use("/public", publicRoutes);

module.exports = router;
