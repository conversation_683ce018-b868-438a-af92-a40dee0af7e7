{"name": "chessbrigade-api", "version": "1.0.0", "description": "", "homepage": "https://github.com/mathi0695/chessbrigade-api#readme", "bugs": {"url": "https://github.com/mathi0695/chessbrigade-api/issues"}, "repository": {"type": "git", "url": "git+https://github.com/mathi0695/chessbrigade-api.git"}, "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"migrate": "npx sequelize-cli db:migrate --config ./config/dbConfig.js", "start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint .", "format": "prettier --write .", "seed": "node scripts/runSeed.js", "seed:reset": "node scripts/runSeed.js --force", "seed:alter": "node scripts/runSeed.js --alter", "seed:strict": "node scripts/runSeed.js --no-ignore-duplicates", "verify:email": "node scripts/verifyEmailConfig.js", "verify:sms": "node scripts/verifySmsConfig.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.772.0", "@aws-sdk/s3-request-presigner": "^3.772.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "helmet": "^8.1.0", "html-pdf": "^3.0.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "node-cron": "^4.0.3", "node-fetch": "^2.6.9", "nodemailer": "^6.10.1", "path": "^0.12.7", "payu-websdk": "^1.2.0", "pg": "^8.14.1", "sequelize": "^6.37.6", "uuid": "^9.0.1", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13", "zod": "^3.24.2"}, "devDependencies": {"sequelize-cli": "^6.6.3"}}