const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");
const tournament = require("../models/tournament");

/**
 * Seed the database with sample data
 * @param {Object} models - The models object from the database configuration
 * @param {Object} options - Options for seeding
 * @param {boolean} options.ignoreDuplicates - Whether to ignore duplicate entries
 * @returns {Promise<void>}
 */
const seedDatabase = async (models, options = {}) => {
  try {
    console.log("Starting database seeding...");

    // Create sample users
    const users = await createUsers(models.User, options);
    console.log("✅ Users created successfully");

    if (!users || !users.clubUsers || users.clubUsers.length === 0) {
      console.log(
        "⚠️ No club users were created. Skipping related data creation."
      );
      return;
    }

    // Create sample club details
    await createClubDetails(models.ClubDetail, users.clubUsers, options);
    console.log("✅ Club details created successfully");

    if (!users.playerUsers || users.playerUsers.length === 0) {
      console.log(
        "⚠️ No player users were created. Skipping player details creation."
      );
    } else {
      // Create sample player details
      await createPlayerDetails(
        models.PlayerDetail,
        users.playerUsers,
        options
      );
      console.log("✅ Player details created successfully");
    }

    // Create sample tournaments
    await createTournaments(models.Tournament, users.clubUsers, options);
    console.log("✅ Tournaments created successfully");

    console.log("Database seeding completed successfully!");

    if (!users.arbiterUsers || users.arbiterUsers.length === 0) {
      console.log("user-details", users.arbiterUsers, users.arbiterUsers);

      console.log(
        "⚠️ No arbiter users were created. Skipping arbiter details creation."
      );
    } else {
      // Create sample arbiter details
      await createArbiterDetails(
        models.ArbiterDetails,
        users.arbiterUsers,
        options
      );
      console.log("✅ Arbiter details created successfully");
    }
  } catch (error) {
    console.error("Error seeding database:", error);
    throw error;
  }
};

/**
 * Create sample users
 * @param {Object} User - The User model
 * @param {Object} options - Options for seeding
 * @returns {Promise<Object>} - Object containing arrays of created users by role
 */
const createUsers = async (User, options = {}) => {
  // Hash password once for all users
  const hashedPassword = await bcrypt.hash("password123", 10);

  // Check for existing users to avoid duplicates
  const existingUsers = await User.findAll({
    attributes: ["email", "name", "cbid"],
  });

  // Create a map of existing users for quick lookup
  const existingEmails = new Set(existingUsers.map((user) => user.email));
  const existingNames = new Set(existingUsers.map((user) => user.name));
  const existingCbids = new Set(existingUsers.map((user) => user.cbid));

  // Sample user data
  const userData = [
    // Admin users
    {
      name: "Admin User",
      cbid: "ADMIN001",
      email: "<EMAIL>",
      phoneNumber: "9876543210",
      password: hashedPassword,
      role: "admin",
    },
    // Arbiter users
    {
      name: "Arbiter User1",
      cbid: "001",
      email: "<EMAIL>",
      phoneNumber: "9873543210",
      password: hashedPassword,
      role: "arbiter",
    },
    {
      name: "Arbiter User2",
      cbid: "002",
      email: "<EMAIL>",
      phoneNumber: "9873543211",
      password: hashedPassword,
      role: "arbiter",
    },
    {
      name: "Arbiter User3",
      cbid: "003",
      email: "<EMAIL>",
      phoneNumber: "9873543212",
      password: hashedPassword,
      role: "arbiter",
    },
    {
      name: "Arbiter User4",
      cbid: "004",
      email: "<EMAIL>",
      phoneNumber: "9873543213",
      password: hashedPassword,
      role: "arbiter",
    },
    {
      name: "Arbiter User5",
      cbid: "005",
      email: "<EMAIL>",
      phoneNumber: "9873543214",
      password: hashedPassword,
      role: "arbiter",
    },
    {
      name: "Arbiter User6",
      cbid: "006",
      email: "<EMAIL>",
      phoneNumber: "9873543215",
      password: hashedPassword,
      role: "arbiter",
    },
    // Club users
    {
      name: "Chennai Chess Club",
      cbid: "CLUB001",
      email: "<EMAIL>",
      phoneNumber: "9876543211",
      password: hashedPassword,
      role: "club",
    },
    {
      name: "Delhi Chess Academy",
      cbid: "CLUB002",
      email: "<EMAIL>",
      phoneNumber: "9876543212",
      password: hashedPassword,
      role: "club",
    },
    {
      name: "Mumbai Chess Association",
      cbid: "CLUB003",
      email: "<EMAIL>",
      phoneNumber: "9876543213",
      password: hashedPassword,
      role: "club",
    },
    // Player users
    {
      name: "Viswanathan Anand",
      cbid: "CB25US00003",
      email: "<EMAIL>",
      phoneNumber: "9876543214",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Koneru Humpy",
      cbid: "CB25US00004",
      email: "<EMAIL>",
      phoneNumber: "9876543215",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Pentala Harikrishna",
      cbid: "CB25US00005",
      email: "<EMAIL>",
      phoneNumber: "9876543216",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "R Praggnanandhaa",
      cbid: "CB25US00006",
      email: "<EMAIL>",
      phoneNumber: "9876543217",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "D Gukesh",
      cbid: "CB25US00007",
      email: "<EMAIL>",
      phoneNumber: "9876543218",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Nihal Sarin",
      cbid: "CB25US00008",
      email: "<EMAIL>",
      phoneNumber: "9876543219",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Arjun Erigaisi",
      cbid: "CB25US00009",
      email: "<EMAIL>",
      phoneNumber: "9876543220",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Harika Dronavalli",
      cbid: "CB25US00010",
      email: "<EMAIL>",
      phoneNumber: "9876543221",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Tania Sachdev",
      cbid: "CB25US00011",
      email: "<EMAIL>",
      phoneNumber: "9876543222",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Vidit Gujrathi",
      cbid: "CB25US00012",
      email: "<EMAIL>",
      phoneNumber: "9876543223",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "B Adhiban",
      cbid: "CB25US00013",
      email: "<EMAIL>",
      phoneNumber: "9876543224",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "S P Sethuraman",
      cbid: "CB25US00014",
      email: "<EMAIL>",
      phoneNumber: "9876543225",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Surya Shekhar Ganguly",
      cbid: "CB25US00015",
      email: "<EMAIL>",
      phoneNumber: "9876543226",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Aravindh Chithambaram",
      cbid: "CB25US00016",
      email: "<EMAIL>",
      phoneNumber: "9876543227",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Sasikiran Krishnan",
      cbid: "CB25US00017",
      email: "<EMAIL>",
      phoneNumber: "9876543228",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Vaishali Rameshbabu",
      cbid: "CB25US00018",
      email: "<EMAIL>",
      phoneNumber: "9876543229",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Divya Deshmukh",
      cbid: "CB25US00019",
      email: "<EMAIL>",
      phoneNumber: "9876543230",
      password: hashedPassword,
      role: "player",
    },
    {
      name: "Abhijeet Gupta",
      cbid: "CB25US00020",
      email: "<EMAIL>",
      phoneNumber: "9876543231",
      password: hashedPassword,
      role: "player",
    },
  ];

  // Filter out users that already exist
  const filteredUserData = userData.filter((user) => {
    const exists =
      existingEmails.has(user.email) ||
      existingNames.has(user.name) ||
      existingCbids.has(user.cbid);
    if (exists) {
      console.log(`Skipping existing user: ${user.name} (${user.email})`);
    }
    return !exists;
  });

  let createdUsers = [];

  if (filteredUserData.length > 0) {
    // Create new users with ignoreDuplicates option
    createdUsers = await User.bulkCreate(filteredUserData, {
      ignoreDuplicates: options.ignoreDuplicates || true,
    });
    console.log(`Created ${createdUsers.length} new users`);
  } else {
    console.log("No new users to create");
  }

  // Get all users including existing ones for the relationships
  const allUsers = await User.findAll();

  // Separate users by role for easier access
  const adminUsers = allUsers.filter((user) => user.role === "admin");
  const clubUsers = allUsers.filter((user) => user.role === "club");
  const playerUsers = allUsers.filter((user) => user.role === "player");
  const arbiterUsers = allUsers.filter((user) => user.role === "arbiter");

  return { adminUsers, clubUsers, playerUsers, allUsers, arbiterUsers };
};

/**
 * Create sample club details
 * @param {Object} ClubDetail - The ClubDetail model
 * @param {Array} clubUsers - Array of club users
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created club details
 */
const createClubDetails = async (ClubDetail, clubUsers, options = {}) => {
  // Check for existing club details to avoid duplicates
  const existingClubDetails = await ClubDetail.findAll({
    attributes: ["userId"],
  });

  // Create a set of existing userIds for quick lookup
  const existingUserIds = new Set(
    existingClubDetails.map((club) => club.userId)
  );

  // Filter club users to only those without existing club details
  const filteredClubUsers = clubUsers.filter((user) => {
    const exists = existingUserIds.has(user.id);
    if (exists) {
      console.log(`Skipping existing club detail for user: ${user.name}`);
    }
    return !exists;
  });

  if (filteredClubUsers.length === 0) {
    console.log("No new club details to create");
    return [];
  }

  const clubDetailsData = filteredClubUsers.map((clubUser, index) => {
    const clubNames = [
      "Chennai Chess Club",
      "Delhi Chess Academy",
      "Mumbai Chess Association",
    ];
    const clubDistrictIds = ["CHN001", "DEL001", "MUM001"];
    const clubIds = [
      "chennai-chess-club",
      "delhi-chess-academy",
      "mumbai-chess-association",
    ];
    const cities = ["Chennai", "Delhi", "Mumbai"];
    const states = ["Tamil Nadu", "Delhi", "Maharashtra"];
    const districts = ["Chennai", "New Delhi", "Mumbai"];

    return {
      clubName: clubNames[index],
      clubId: clubIds[index],
      clubDistrictId: clubDistrictIds[index],
      userId: clubUser.id,
      tournamentStatus: "inactive",
      contactPersonName: `Contact Person ${index + 1}`,
      contactPersonNumber: `987654321${index}`,
      contactPersonEmail: `contact${index + 1}@chessbrigade.com`,
      alternateContactNumber: `876543210${index}`,
      country: "India",
      state: states[index],
      district: districts[index],
      city: cities[index],
      pincode: `60001${index}`,
      address: `123 Chess Street, ${cities[index]}, ${states[index]}`,
      locationUrl: `https://maps.google.com/maps?q=${cities[index]}+${states[index]}`,
      authorizedSignatoryName: `Signatory ${index + 1}`,
      authorizedSignatoryContactNumber: `*********${index}`,
      authorizedSignatoryEmail: `signatory${index + 1}@chessbrigade.com`,
      authorizedSignatoryDesignation: "President",
      bankName: "State Bank of India",
      AccountNumber: `**********${index}`,
      branchIFSCCode: `SBIN0001${index}`,
      branchName: `SBI ${cities[index]} Branch`,
      spotEntry: false,
      bankAccountType: "Current",
      bankAccountHolderName: clubNames[index],
      countryCode: "IN",
      stateCode: states[index].substring(0, 2).toUpperCase(),
      profileUrl: `https://example.com/clubs/${index + 1}.jpg`,
    };
  });

  return await ClubDetail.bulkCreate(clubDetailsData, {
    ignoreDuplicates: options.ignoreDuplicates || true,
  });
};

/**
 * Create sample player details
 * @param {Object} PlayerDetail - The PlayerDetail model
 * @param {Array} playerUsers - Array of player users
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created player details
 */
const createPlayerDetails = async (PlayerDetail, playerUsers, options = {}) => {
  // Check for existing player details to avoid duplicates
  const existingPlayerDetails = await PlayerDetail.findAll({
    attributes: ["userId"],
  });

  // Create a set of existing userIds for quick lookup
  const existingUserIds = new Set(
    existingPlayerDetails.map((player) => player.userId)
  );

  // Filter player users to only those without existing player details
  const filteredPlayerUsers = playerUsers.filter((user) => {
    const exists = existingUserIds.has(user.id);
    if (exists) {
      console.log(`Skipping existing player detail for user: ${user.name}`);
    }
    return !exists;
  });

  if (filteredPlayerUsers.length === 0) {
    console.log("No new player details to create");
    return [];
  }

  // Fetch club details to get their IDs
  const { ClubDetail } = require("../config/db").models;
  const clubDetails = await ClubDetail.findAll();

  // Create a map of club names to their IDs
  const clubNameToIdMap = {};
  clubDetails.forEach((club) => {
    clubNameToIdMap[club.clubName] = club.id;
  });

  console.log(
    "Club details fetched for player assignments:",
    Object.keys(clubNameToIdMap).map(
      (name) => `${name}: ${clubNameToIdMap[name]}`
    )
  );

  const playerDetailsData = filteredPlayerUsers.map((playerUser, index) => {
    const playerNames = [
      "Viswanathan Anand",
      "Koneru Humpy",
      "Pentala Harikrishna",
      "R Praggnanandhaa",
      "D Gukesh",
      "Nihal Sarin",
      "Arjun Erigaisi",
      "Harika Dronavalli",
      "Tania Sachdev",
      "Vidit Gujrathi",
      "B Adhiban",
      "S P Sethuraman",
      "Surya Shekhar Ganguly",
      "Aravindh Chithambaram",
      "Sasikiran Krishnan",
      "Vaishali Rameshbabu",
      "Divya Deshmukh",
      "Abhijeet Gupta",
    ];
    const fideRatings = [
      "2756",
      "2586",
      "2717",
      "2684",
      "2758",
      "2651",
      "2748",
      "2502",
      "2392",
      "2702",
      "2641",
      "2620",
      "2608",
      "2614",
      "2638",
      "2442",
      "2419",
      "2622",
    ];
    const fideIds = [
      "5000017",
      "5002150",
      "5007003",
      "25002848",
      "46616543",
      "25095080",
      "35009192",
      "5007003",
      "5006120",
      "35009192",
      "5018471",
      "5021596",
      "5002192",
      "5072204",
      "5004985",
      "25008838",
      "46616330",
      "5018358",
    ];
    const aicfIds = [
      "AICF001",
      "AICF002",
      "AICF003",
      "AICF004",
      "AICF005",
      "AICF006",
      "AICF007",
      "AICF008",
      "AICF009",
      "AICF010",
      "AICF011",
      "AICF012",
      "AICF013",
      "AICF014",
      "AICF015",
      "AICF016",
      "AICF017",
      "AICF018",
    ];
    const cities = [
      "Chennai",
      "Vijayawada",
      "Guntur",
      "Chennai",
      "Chennai",
      "Thrissur",
      "Warangal",
      "Guntur",
      "Delhi",
      "Nashik",
      "Chennai",
      "Chennai",
      "Kolkata",
      "Chennai",
      "Chennai",
      "Chennai",
      "Nagpur",
      "Gurgaon",
    ];
    // Define gender for each player (male or female)
    const genders = [
      "male", // Viswanathan Anand
      "female", // Koneru Humpy
      "male", // Pentala Harikrishna
      "male", // R Praggnanandhaa
      "male", // D Gukesh
      "male", // Nihal Sarin
      "male", // Arjun Erigaisi
      "female", // Harika Dronavalli
      "female", // Tania Sachdev
      "male", // Vidit Gujrathi
      "male", // B Adhiban
      "male", // S P Sethuraman
      "male", // Surya Shekhar Ganguly
      "male", // Aravindh Chithambaram
      "male", // Sasikiran Krishnan
      "female", // Vaishali Rameshbabu
      "female", // Divya Deshmukh
      "male", // Abhijeet Gupta
    ];
    const states = [
      "Tamil Nadu",
      "Andhra Pradesh",
      "Andhra Pradesh",
      "Tamil Nadu",
      "Tamil Nadu",
      "Kerala",
      "Telangana",
      "Andhra Pradesh",
      "Delhi",
      "Maharashtra",
      "Tamil Nadu",
      "Tamil Nadu",
      "West Bengal",
      "Tamil Nadu",
      "Tamil Nadu",
      "Tamil Nadu",
      "Maharashtra",
      "Haryana",
    ];
    const districts = [
      "Chennai",
      "Krishna",
      "Guntur",
      "Chennai",
      "Chennai",
      "Thrissur",
      "Warangal",
      "Guntur",
      "New Delhi",
      "Nashik",
      "Chennai",
      "Chennai",
      "Kolkata",
      "Chennai",
      "Chennai",
      "Chennai",
      "Nagpur",
      "Gurgaon",
    ];
    const dobs = [
      "1969-12-11",
      "1987-03-31",
      "1986-05-10",
      "2005-08-10",
      "2006-05-29",
      "2004-07-13",
      "2003-09-03",
      "1991-01-12",
      "1986-08-20",
      "1994-10-24",
      "1992-08-15",
      "1993-02-25",
      "1983-02-24",
      "1999-09-18",
      "1981-01-07",
      "2001-06-21",
      "2005-08-03",
      "1989-10-16",
    ];

    return {
      playerTitle: [1, 7, 8, 15, 16].includes(index) ? "IM" : "GM",
      playerName: playerNames[index],
      profileUrl: `https://example.com/players/${index + 1}.jpg`,
      userId: playerUser.id,
      dob: dobs[index],
      gender: genders[index],
      parentGuardianName: index > 2 ? "Parent Name" : null,
      emergencyContact: `987654321${index}`,
      alternateContact: `876543210${index}`,
      fideRating: fideRatings[index],
      fideId: fideIds[index],
      aicfId: aicfIds[index],
      stateId: `STATE${index + 1}`,
      districtId: `DIST${index + 1}`,
      association: "All India Chess Federation",
      // Determine club name based on player index
      club: (() => {
        // Mumbai Chess Association - 5 players
        if ([0, 5, 9, 13, 17].includes(index)) {
          return "Mumbai Chess Association";
        }
        // Delhi Chess Academy - 5 players
        else if ([1, 6, 8, 11, 16].includes(index)) {
          return "Delhi Chess Academy";
        }
        // Chennai Chess Club - 3 players + remaining players
        else if (
          [3, 4, 15].includes(index) ||
          ![0, 1, 5, 6, 8, 9, 11, 13, 16, 17].includes(index)
        ) {
          return "Chennai Chess Club";
        } else {
          return "Mumbai Chess Association";
        }
      })(),

      // Set clubId based on the club name
      clubId: (() => {
        let clubName;
        // Mumbai Chess Association - 5 players
        if ([0, 5, 9, 13, 17].includes(index)) {
          clubName = "Mumbai Chess Association";
        }
        // Delhi Chess Academy - 5 players
        else if ([1, 6, 8, 11, 16].includes(index)) {
          clubName = "Delhi Chess Academy";
        }
        // Chennai Chess Club - 3 players + remaining players
        else if (
          [3, 4, 15].includes(index) ||
          ![0, 1, 5, 6, 8, 9, 11, 13, 16, 17].includes(index)
        ) {
          clubName = "Chennai Chess Club";
        } else {
          clubName = "Mumbai Chess Association";
        }
        return clubNameToIdMap[clubName];
      })(),
      country: "India",
      state: states[index],
      district: districts[index],
      city: cities[index],
      pincode: `60001${index}`,
      address: `123 Chess Street, ${cities[index]}, ${states[index]}`,
      termsAndConditions: true,
    };
  });

  return await PlayerDetail.bulkCreate(playerDetailsData, {
    ignoreDuplicates: options.ignoreDuplicates || true,
  });
};

/**
 * Create sample tournaments
 * @param {Object} Tournament - The Tournament model
 * @param {Array} clubUsers - Array of club users
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created tournaments
 */
const createTournaments = async (Tournament, clubUsers, options = {}) => {
  // Check for existing tournaments to avoid duplicates
  const existingTournaments = await Tournament.findAll({
    attributes: ["title"],
  });

  // Create a set of existing tournament titles for quick lookup
  const existingTitles = new Set(
    existingTournaments.map((tournament) => tournament.title)
  );

  const tournamentData = [];

  // Create 2 tournaments for each club
  clubUsers.forEach((clubUser) => {
    const levels = ["national", "state", "district", "global"];
    const systems = ["swiss", "round-robin", "knockout"];

    for (let i = 0; i < 2; i++) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 30 * (i + 1)); // 1 or 2 months in the future

      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 3); // 3-day tournament

      const registrationEndDate = new Date(startDate);
      registrationEndDate.setDate(registrationEndDate.getDate() - 2); // 2 days before start

      const tournamentTitle = `${clubUser.name
        .toLowerCase()
        .replace(/\s+/g, "-")}-championship-${i + 1}`;
      const reportingTime = "09:00 AM";

      // Skip if tournament with this title already exists
      if (existingTitles.has(tournamentTitle)) {
        console.log(`Skipping existing tournament: ${tournamentTitle}`);
        continue;
      }

      tournamentData.push({
        clubId: clubUser.id,
        title: tournamentTitle,
        fideRated: i === 0, // First tournament is FIDE rated
        organizerName: clubUser.name,
        tournamentLevel: levels[Math.floor(Math.random() * levels.length)],
        startDate: startDate.toISOString().split("T")[0],
        endDate: endDate.toISOString().split("T")[0],
        registrationStartDate: startDate.toISOString().split("T")[0],
        registrationEndDate: registrationEndDate.toISOString().split("T")[0],
        registrationEndTime: "11:59 PM",
        arbiterId:null,
        tournamentDirectorName: "Tournament Director",
        entryFeeCurrency: "INR",
        entryFee: 500 * (i + 1),
        numberOfRounds: 5 + i,
        timeControl: "classical",
        reportingTime: reportingTime,
        timeControlDuration: "90",
        timeControlIncrement: "30",
        tournamentType: "single",
        tournamentSystem: systems[Math.floor(Math.random() * systems.length)],
        nationalApproval: "Approved",
        stateApproval: "Approved",
        districtApproval: "Approved",
        contactPersonName: `Contact Person ${i + 1}`,
        email: `tournament${i + 1}@${clubUser.email.split("@")[1]}`,
        contactNumber: clubUser.phoneNumber,
        alternateContactNumber: `876543210${i}`,
        numberOfTrophiesMale: 3,
        numberOfTrophiesFemale: 3,
        totalCashPrizeCurrency: "INR",
        totalCashPrizeAmount: 10000,
        country: "India",
        state: clubUser.name.includes("Chennai")
          ? "Tamil Nadu"
          : clubUser.name.includes("Delhi")
          ? "Delhi"
          : "Maharashtra",
        district: clubUser.name.includes("Chennai")
          ? "Chennai"
          : clubUser.name.includes("Delhi")
          ? "New Delhi"
          : "Mumbai",
        city: clubUser.name.includes("Chennai")
          ? "Chennai"
          : clubUser.name.includes("Delhi")
          ? "Delhi"
          : "Mumbai",
        pincode: clubUser.name.includes("Chennai")
          ? "600001"
          : clubUser.name.includes("Delhi")
          ? "110001"
          : "400001",
        venueAddress: `123 Chess Street, ${
          clubUser.name.includes("Chennai")
            ? "Chennai"
            : clubUser.name.includes("Delhi")
            ? "Delhi"
            : "Mumbai"
        }`,
        nearestLandmark: "Chess Park",
        brochureUrl: `https://example.com/tournaments/${i + 1}.pdf`,
        locationUrl: `https://maps.google.com/maps?q=chennai+tamil+nadu`,
        chessboardProvided: Math.random() < 0.5,
        timerProvided: Math.random() < 0.5,
        parkingFacility: ["yes", "no", "limited"][
          Math.floor(Math.random() * 3)
        ],
        spotEntry: Math.random() < 0.3,
        tournamentStatus: "inactive",
        foodFacility: [
          "breakfast",
          "lunch",
          "dinner",
          "snacks",
          "beverages",
          "nil",
        ]
          .sort(() => 0.5 - Math.random())
          .slice(0, Math.floor(Math.random() * 6) + 1)
          .join(","),
        maleAgeCategory: ["OPEN", "U18", "U15", "U12"]
          .sort(() => 0.5 - Math.random())
          .slice(0, Math.floor(Math.random() * 4) + 1),
        femaleAgeCategory: ["OPEN", "U18", "U15", "U12"]
          .sort(() => 0.5 - Math.random())
          .slice(0, Math.floor(Math.random() * 4) + 1),
        tournamentCategory: ["open", "male", "female"][
          Math.floor(Math.random() * 3)
        ],
      });
    }
  });

  if (tournamentData.length === 0) {
    console.log("No new tournaments to create");
    return [];
  }

  return await Tournament.bulkCreate(tournamentData, {
    ignoreDuplicates: options.ignoreDuplicates || true,
  });
};

/**
 * Create sample arbiter details
 * @param {Object} ArbiterDetails - The ArbiterDetails model
 * @param {Array} arbiterUsers - Array of arbiter users (with role = "arbiter")
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created arbiter details
 */
const createArbiterDetails = async (
  ArbiterDetails,
  arbiterUsers,
  options = {}
) => {
  // Fetch existing arbiter details to avoid duplication
  const existingArbiters = await ArbiterDetails.findAll({
    attributes: ["userId"],
  });

  const existingUserIds = new Set(existingArbiters.map((a) => a.userId));

  // Filter out users who already have arbiter details
  const filteredUsers = arbiterUsers.filter((user) => {
    const exists = existingUserIds.has(user.id);
    if (exists) {
      console.log(`Skipping existing arbiter detail for user: ${user.name}`);
    }
    return !exists;
  });

  if (filteredUsers.length === 0) {
    console.log("No new arbiter details to create");
    return [];
  }

  const titles = ["GM", "IM", "FM", "WGM", "WIM", "WFM"];
  const countries = ["India", "India", "India"];
  const states = ["Tamil Nadu", "Delhi", "Maharashtra"];
  const districts = ["Chennai", "New Delhi", "Mumbai"];
  const cities = ["Chennai", "Delhi", "Mumbai"];

  const arbiterDetailsData = filteredUsers.map((user, index) => ({
    userId: user.id,
    profileUrl: `https://example.com/arbiters/${index + 1}.jpg`,
    officialId: `ARB${1000 + index}`,
    title: titles[index % titles.length],
    phoneNumber: `987654321${index}`,
    email: `arbiter${index + 1}@chessbrigade.com`,
    alternateContactNumber: `876543210${index}`,
    country: countries[index % countries.length],
    state: states[index % states.length],
    district: districts[index % districts.length],
    city: cities[index % cities.length],
    pincode: `6000${index + 1}`,
  }));

  return await ArbiterDetails.bulkCreate(arbiterDetailsData, {
    ignoreDuplicates: options.ignoreDuplicates || true,
  });
};

module.exports = seedDatabase;
