const fs = require('fs');
const path = require('path');
const { sendResponse, handleError } = require("../../utils/apiResponse");
const emailService = require("../../utils/mailer/emailService");
const smsService = require("../../utils/sms/smsService");
const { z } = require("zod");
const { v4: uuidv4 } = require('uuid');

/**
 * Get all available email templates
 */
// const getEmailTemplates = async (req, res) => {
//   try {
//     const templatesDir = path.join(__dirname, '../../utils/mailer/templates');
//     const allowedTemplateIds = [
//      "tournament-promotion",
//      "custom"
//     ];

//     const templateFiles = fs
//       .readdirSync(templatesDir)
//       .filter(file => file.endsWith('.hbs'))
//       .filter(file => allowedTemplateIds.includes(file.replace('.hbs', ''))); // Filter step

//     const templates = templateFiles.map(file => {
//       const templateName = file.replace('.hbs', '');
//       const templatePath = path.join(templatesDir, file);
//       const templateContent = fs.readFileSync(templatePath, 'utf8');

//       const variableMatches = templateContent.match(/\{\{([^}]+)\}\}/g) || [];
//       const variables = [...new Set(variableMatches.map(match =>
//         match.replace(/[{}]/g, '').trim()
//       ))];

//       return {
//         id: templateName,
//         name: formatTemplateName(templateName),
//         category: getTemplateCategory(templateName),
//         description: getTemplateDescription(templateName),
//         variables,
//         content: templateContent
//       };
//     });

//     const categorizedTemplates = templates.reduce((acc, template) => {
//       if (!acc[template.category]) {
//         acc[template.category] = [];
//       }
//       acc[template.category].push(template);
//       return acc;
//     }, {});

//     sendResponse(res, 200, {
//       success: true,
//       data: {
//         templates: categorizedTemplates,
//         totalTemplates: templates.length
//       }
//     });
//   } catch (error) {
//     handleError(res, error);
//   }
// };


/**
 * Get all available SMS templates
 */
const getSmsTemplates = async (req, res) => {
  try {
    // SMS templates are predefined in the service
    const templates = [
      {
        id: 'welcome',
        name: 'Welcome SMS',
        category: 'user',
        description: 'Welcome message for new users',
        variables: ['VAR1', 'VAR2'], // VAR1: name, VAR2: frontend_url
        templateId: '', // Will be filled from config
        maxLength: 160
      },
    ];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get all available WhatsApp templates
 */
const getWhatsappTemplates = async (req, res) => {
  try {
    // WhatsApp templates (similar to SMS for now)
    const templates = [
      {
        id: 'welcome',
        name: 'Welcome Message',
        category: 'user',
        description: 'Welcome message for new users',
        variables: ['name', 'platform_url'],
        maxLength: 4096
      },
    ];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Helper function to format template names
 */
function formatTemplateName(templateName) {
  return templateName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Helper function to get template category
 */
function getTemplateCategory(templateName) {
  if (templateName.includes('tournament')) return 'tournament';
  if (templateName.includes('club')) return 'club';
  if (templateName.includes('payment')) return 'payment';
  if (templateName.includes('friend')) return 'social';
  if (templateName.includes('password') || templateName.includes('welcome')) return 'authentication';
  return 'general';
}

/**
 * Helper function to get template description
 */
function getTemplateDescription(templateName) {
  const descriptions = {
    'welcome': 'Welcome email for new users',
    'password-reset': 'Password reset email with OTP',
    'tournament-registration': 'Tournament registration confirmation',
    'tournament-reminder': 'Tournament registration reminder',
    'tournament-cancel-refund': 'Tournament cancellation and refund notification',
    'tournament-date-changed': 'Tournament date change notification',
    'tournament-refund-initiated': 'Refund initiation notification',
    'tournament-refund-successful': 'Successful refund notification',
    'payment-confirmation': 'Payment confirmation email',
    'payment-failure': 'Payment failure notification',
    'club-invitation': 'Club invitation email',
    'club-enquiry': 'Club enquiry notification',
    'club-accept-request': 'Club membership acceptance',
    'club-arbiter-accept': 'Arbiter acceptance by club',
    'club-player-invite': 'Player invitation to club',
    'club-remove-player': 'Player removal from club',
    'player-accept-club': 'Player accepts club invitation',
    'player-club-invite': 'Player invitation to join club',
    'player-leave-club': 'Player leaves club notification',
    'friend-request': 'Friend request notification',
    'friend-request-accept': 'Friend request acceptance',
    'friend-removed': 'Friend removal notification',
    'arbiter-tournament-invite': 'Arbiter tournament invitation',
    'contact-enquiry': 'Contact form enquiry notification'
  };

  return descriptions[templateName] || 'Email template';
}

   const schema = z.object({
      recipients: z.array(z.object({
        email: z.string().email(),
        name: z.string().optional(),
        id: z.string().optional(),
        type: z.enum(['player', 'club', 'arbiter']).optional()
      })).min(1, "At least one recipient is required"),
      subject: z.string().min(1, "Subject is required"),
      content: z.string().optional(),
      recipientType: z.enum(['player', 'club', 'arbiter']).optional(),
      bulkSend: z.boolean().optional(),
      searchFilters: z.object({
        playerName: z.string().optional(),
        playerId: z.string().optional(),
        clubName: z.string().optional(),
        arbiterName: z.string().optional(),
        arbiterId: z.string().optional(),
        email: z.string().email().optional(),
        country: z.string().optional(),
        state: z.string().optional(),
        district: z.string().optional(),
        city: z.string().optional()
      }).optional()
    });
/**
 * Send bulk email
 */
const sendSelectedBulkEmail = async (req, res) => {
  try {


    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }
    const { recipients, subject, content, recipientType, bulkSend, searchFilters } = data;

    // Validate that either template or custom content is provided
    if (recipients.length === 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "No recipients provided"
      });
    }

    const results = [];
    const errors = [];

    // Send emails to all recipients
    for (const recipient of recipients) {
      try {
        let emailResult;


          // Use custom content (create a temporary template)
          emailResult = await emailService.sendCustomEmail({
            to: recipient.email,
            subject,
            templateName: 'custom', // You'll need to create a generic custom template
            templateData: {
              customContent,
              name: recipient.name || 'User',
              email: recipient.email
            }
          });


        results.push({
          recipient: recipient.email,
          status: 'sent',
          messageId: emailResult.messageId
        });
      } catch (error) {
        errors.push({
          recipient: recipient.email,
          status: 'failed',
          error: error.message
        });
      }
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        totalRecipients: recipients.length,
        successful: results.length,
        failed: errors.length,
        results,
        errors
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};
const sendBulkEmail = async (req, res) => {
  try {


    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }
    const { recipients, subject, content, recipientType, } = data;

    // Validate that either template or custom content is provided
    if (recipients.length === 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "No recipients provided"
      });
    }

    const results = [];
    const errors = [];

    // Send emails to all recipients
    for (const recipient of recipients) {
      try {
        let emailResult;


          // Use custom content (create a temporary template)
          emailResult = await emailService.sendCustomEmail({
            to: recipient.email,
            subject,
            html: content
          });


        results.push({
          recipient: recipient.email,
          status: 'sent',
          messageId: emailResult.messageId
        });
      } catch (error) {
        errors.push({
          recipient: recipient.email,
          status: 'failed',
          error: error.message
        });
      }
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        totalRecipients: recipients.length,
        successful: results.length,
        failed: errors.length,
        results,
        errors
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Send bulk SMS
 */
const sendBulkSms = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z.array(z.object({
        mobile: z.string().min(10, "Valid mobile number is required"),
        name: z.string().optional(),
        id: z.string().optional()
      })).min(1, "At least one recipient is required"),
      templateId: z.string().min(1, "Template ID is required"),
      variables: z.object({}).optional()
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }

    const { recipients, templateId, variables = {} } = data;

    const results = [];
    const errors = [];

    // Send SMS to all recipients
    for (const recipient of recipients) {
      try {
        const smsResult = await smsService.sendCustomSMS({
          templateId,
          mobile: recipient.mobile,
          variables: {
            ...variables,
            VAR1: recipient.name || 'User'
          }
        });

        results.push({
          recipient: recipient.mobile,
          status: 'sent',
          messageId: smsResult.messageId || smsResult.request_id
        });
      } catch (error) {
        errors.push({
          recipient: recipient.mobile,
          status: 'failed',
          error: error.message
        });
      }
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        totalRecipients: recipients.length,
        successful: results.length,
        failed: errors.length,
        results,
        errors
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Send bulk WhatsApp messages
 */
const sendBulkWhatsapp = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z.array(z.object({
        mobile: z.string().min(10, "Valid mobile number is required"),
        name: z.string().optional(),
        id: z.string().optional()
      })).min(1, "At least one recipient is required"),
      templateId: z.string().min(1, "Template ID is required"),
      variables: z.object({}).optional()
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }

    const { recipients, templateId, variables = {} } = data;

    // For now, WhatsApp will use the same SMS service
    // In the future, you can implement a dedicated WhatsApp service
    const results = [];
    const errors = [];

    // Send WhatsApp messages to all recipients
    for (const recipient of recipients) {
      try {
        // Placeholder for WhatsApp implementation
        // For now, we'll simulate success
        results.push({
          recipient: recipient.mobile,
          status: 'sent',
          messageId: `wa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        });
      } catch (error) {
        errors.push({
          recipient: recipient.mobile,
          status: 'failed',
          error: error.message
        });
      }
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        totalRecipients: recipients.length,
        successful: results.length,
        failed: errors.length,
        results,
        errors,
        note: "WhatsApp integration is not yet implemented. This is a simulation."
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  // getEmailTemplates,
  getSmsTemplates,
  getWhatsappTemplates,
  sendBulkEmail,
  sendBulkSms,
  sendBulkWhatsapp
};
