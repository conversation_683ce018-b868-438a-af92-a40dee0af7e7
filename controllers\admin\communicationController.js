const fs = require('fs');
const path = require('path');
const { sendResponse, handleError } = require("../../utils/apiResponse");
const emailService = require("../../utils/mailer/emailService");
const smsService = require("../../utils/sms/smsService");
const { z } = require("zod");
const { v4: uuidv4 } = require('uuid');

/**
 * Get all available email templates
 */
const getEmailTemplates = async (req, res) => {
  try {
    const templatesDir = path.join(__dirname, '../../utils/mailer/templates');
    const templateFiles = fs.readdirSync(templatesDir).filter(file => file.endsWith('.hbs'));

    const templates = templateFiles.map(file => {
      const templateName = file.replace('.hbs', '');
      const templatePath = path.join(templatesDir, file);
      const templateContent = fs.readFileSync(templatePath, 'utf8');

      // Extract template variables from handlebars template
      const variableMatches = templateContent.match(/\{\{([^}]+)\}\}/g) || [];
      const variables = [...new Set(variableMatches.map(match =>
        match.replace(/[{}]/g, '').trim()
      ))];

      return {
        id: templateName,
        name: formatTemplateName(templateName),
        category: getTemplateCategory(templateName),
        description: getTemplateDescription(templateName),
        variables,
        content: templateContent
      };
    });

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get all available SMS templates
 */
const getSmsTemplates = async (req, res) => {
  try {
    // SMS templates are predefined in the service
    const templates = [
      {
        id: 'welcome',
        name: 'Welcome SMS',
        category: 'user',
        description: 'Welcome message for new users',
        variables: ['VAR1', 'VAR2'], // VAR1: name, VAR2: frontend_url
        templateId: process.env.MSG91_WELCOME_TEMPLATE_ID || '',
        maxLength: 160
      },
      {
        id: 'otp',
        name: 'OTP Verification',
        category: 'authentication',
        description: 'OTP verification message',
        variables: ['VAR1', 'VAR2'], // VAR1: otp, VAR2: expiryMinutes
        templateId: process.env.MSG91_OTP_TEMPLATE_ID || '',
        maxLength: 160
      },
      {
        id: 'registration-confirmation',
        name: 'Registration Confirmation',
        category: 'tournament',
        description: 'Tournament registration confirmation',
        variables: ['VAR1', 'VAR2', 'VAR3'], // VAR1: playerName, VAR2: tournamentName, VAR3: registrationId
        templateId: process.env.MSG91_REGISTRATION_TEMPLATE_ID || '',
        maxLength: 160
      },
      {
        id: 'payment-confirmation',
        name: 'Payment Confirmation',
        category: 'payment',
        description: 'Payment confirmation message',
        variables: ['VAR1', 'VAR2', 'VAR3'], // VAR1: playerName, VAR2: amount, VAR3: transactionId
        templateId: process.env.MSG91_PAYMENT_TEMPLATE_ID || '',
        maxLength: 160
      },
      {
        id: 'club-invitation',
        name: 'Club Invitation',
        category: 'club',
        description: 'Club invitation message',
        variables: ['VAR1', 'VAR2'], // VAR1: playerName, VAR2: clubName
        templateId: process.env.MSG91_CLUB_INVITE_TEMPLATE_ID || '',
        maxLength: 160
      },
      {
        id: 'tournament-pairing',
        name: 'Tournament Pairing',
        category: 'tournament',
        description: 'Tournament pairing notification',
        variables: ['roundno', 'boardno', 'moredetailslink'],
        templateId: process.env.MSG91_PAIRING_TEMPLATE_ID || '',
        maxLength: 160
      }
    ];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get all available WhatsApp templates
 */
const getWhatsappTemplates = async (req, res) => {
  try {
    // WhatsApp templates (similar to SMS for now)
    const templates = [
      {
        id: 'welcome',
        name: 'Welcome Message',
        category: 'user',
        description: 'Welcome message for new users',
        variables: ['name', 'platform_url'],
        maxLength: 4096
      },
      {
        id: 'tournament-registration',
        name: 'Tournament Registration',
        category: 'tournament',
        description: 'Tournament registration confirmation',
        variables: ['playerName', 'tournamentName', 'startDate', 'venue'],
        maxLength: 4096
      },
      {
        id: 'payment-confirmation',
        name: 'Payment Confirmation',
        category: 'payment',
        description: 'Payment confirmation message',
        variables: ['playerName', 'amount', 'transactionId', 'tournamentName'],
        maxLength: 4096
      },
      {
        id: 'club-invitation',
        name: 'Club Invitation',
        category: 'club',
        description: 'Club invitation message',
        variables: ['playerName', 'clubName', 'inviterName'],
        maxLength: 4096
      }
    ];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Helper function to format template names
 */
function formatTemplateName(templateName) {
  return templateName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Helper function to get template category
 */
function getTemplateCategory(templateName) {
  if (templateName.includes('tournament')) return 'tournament';
  if (templateName.includes('club')) return 'club';
  if (templateName.includes('payment')) return 'payment';
  if (templateName.includes('friend')) return 'social';
  if (templateName.includes('password') || templateName.includes('welcome')) return 'authentication';
  return 'general';
}

/**
 * Helper function to get template description
 */
function getTemplateDescription(templateName) {
  const descriptions = {
    'welcome': 'Welcome email for new users',
    'password-reset': 'Password reset email with OTP',
    'tournament-registration': 'Tournament registration confirmation',
    'tournament-reminder': 'Tournament registration reminder',
    'tournament-cancel-refund': 'Tournament cancellation and refund notification',
    'tournament-date-changed': 'Tournament date change notification',
    'tournament-refund-initiated': 'Refund initiation notification',
    'tournament-refund-successful': 'Successful refund notification',
    'payment-confirmation': 'Payment confirmation email',
    'payment-failure': 'Payment failure notification',
    'club-invitation': 'Club invitation email',
    'club-enquiry': 'Club enquiry notification',
    'club-accept-request': 'Club membership acceptance',
    'club-arbiter-accept': 'Arbiter acceptance by club',
    'club-player-invite': 'Player invitation to club',
    'club-remove-player': 'Player removal from club',
    'player-accept-club': 'Player accepts club invitation',
    'player-club-invite': 'Player invitation to join club',
    'player-leave-club': 'Player leaves club notification',
    'friend-request': 'Friend request notification',
    'friend-request-accept': 'Friend request acceptance',
    'friend-removed': 'Friend removal notification',
    'arbiter-tournament-invite': 'Arbiter tournament invitation',
    'contact-enquiry': 'Contact form enquiry notification',
    'custom': 'Custom email content'
  };

  return descriptions[templateName] || 'Email template';
}

/**
 * Send bulk email - Creates notifications in database for reliable delivery
 */
const sendBulkEmail = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z.array(z.object({
        email: z.string().email(),
        name: z.string().optional(),
        id: z.string().optional()
      })).min(1, "At least one recipient is required"),
      subject: z.string().min(1, "Subject is required"),
      templateName: z.string().optional(),
      customContent: z.string().optional(),
      templateData: z.object({}).optional(),
      priority: z.number().int().min(1).max(5).default(2),
      expiresInHours: z.number().int().min(1).max(168).default(24)
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }

    const {
      recipients,
      subject,
      templateName,
      customContent,
      templateData = {},
      priority,
      expiresInHours
    } = data;

    // Validate that either template or custom content is provided
    if (!templateName && !customContent) {
      return sendResponse(res, 400, {
        success: false,
        error: "Either templateName or customContent is required"
      });
    }

    const { Notifications } = require("../../config/db").models;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expiresInHours * 60 * 60 * 1000));

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      // Determine notification type based on template
      let notificationType = 'promotional';
      if (templateName) {
        if (templateName.includes('tournament')) notificationType = 'tournament-registration';
        else if (templateName.includes('payment')) notificationType = 'payment-confirmation';
        else if (templateName.includes('club')) notificationType = 'club-invitation';
        else if (templateName.includes('friend')) notificationType = 'friend-request';
      }

      // Prepare email content for custom content
      let emailContent = null;
      if (customContent) {
        emailContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: #2c5aa0; color: white; padding: 20px; text-align: center;">
              <h1>Chess Brigade</h1>
            </div>
            <div style="padding: 20px;">
              <h2>Hello ${recipient.name || 'Chess Enthusiast'},</h2>
              ${customContent}
            </div>
            <div style="background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
              <p>© ${new Date().getFullYear()} Chess Brigade. All rights reserved.</p>
            </div>
          </div>
        `;
      }

      const notification = {
        userId: recipient.id || null,
        creatorId,
        email: recipient.email,
        type: notificationType,
        platform: 'email',
        templateId: templateName || 'custom',
        content: {
          subject,
          templateData: templateName ? {
            ...templateData,
            name: recipient.name || 'User',
            email: recipient.email,
            currentYear: new Date().getFullYear()
          } : null,
          customContent: emailContent,
          recipientName: recipient.name || 'User'
        },
        status: 'pending',
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkEmail: true,
          batchId,
          createdBy: creatorId,
          originalSubject: subject,
          hasCustomContent: !!customContent,
          templateUsed: templateName || 'custom'
        }
      };

      notifications.push(notification);
    }

    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      const created = await Notifications.bulkCreate(chunk);
      createdNotifications = createdNotifications.concat(created);
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: 'queued',
        message: 'Emails have been queued for delivery. They will be processed by the notification service.',
        estimatedDeliveryTime: '5-15 minutes',
        expiresAt
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Send bulk SMS - Creates notifications in database for reliable delivery
 */
const sendBulkSms = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z.array(z.object({
        mobile: z.string().min(10, "Valid mobile number is required"),
        name: z.string().optional(),
        id: z.string().optional()
      })).min(1, "At least one recipient is required"),
      templateId: z.string().min(1, "Template ID is required"),
      variables: z.object({}).optional(),
      priority: z.number().int().min(1).max(5).default(2),
      expiresInHours: z.number().int().min(1).max(168).default(24)
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }

    const { recipients, templateId, variables = {}, priority, expiresInHours } = data;

    const { Notifications } = require("../../config/db").models;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expiresInHours * 60 * 60 * 1000));

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      const notification = {
        userId: recipient.id || null,
        creatorId,
        phoneNumber: recipient.mobile,
        type: 'promotional',
        platform: 'sms',
        templateId,
        content: {
          ...variables,
          VAR1: recipient.name || 'User',
          recipientName: recipient.name || 'User'
        },
        status: 'pending',
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkSms: true,
          batchId,
          createdBy: creatorId,
          templateUsed: templateId
        }
      };

      notifications.push(notification);
    }

    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      const created = await Notifications.bulkCreate(chunk);
      createdNotifications = createdNotifications.concat(created);
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: 'queued',
        message: 'SMS messages have been queued for delivery. They will be processed by the notification service.',
        estimatedDeliveryTime: '5-15 minutes',
        expiresAt
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Send bulk WhatsApp messages - Creates notifications in database for reliable delivery
 */
const sendBulkWhatsapp = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z.array(z.object({
        mobile: z.string().min(10, "Valid mobile number is required"),
        name: z.string().optional(),
        id: z.string().optional()
      })).min(1, "At least one recipient is required"),
      templateId: z.string().min(1, "Template ID is required"),
      variables: z.object({}).optional(),
      priority: z.number().int().min(1).max(5).default(2),
      expiresInHours: z.number().int().min(1).max(168).default(24)
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors
      });
    }

    const { recipients, templateId, variables = {}, priority, expiresInHours } = data;

    const { Notifications } = require("../../config/db").models;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expiresInHours * 60 * 60 * 1000));

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      const notification = {
        userId: recipient.id || null,
        creatorId,
        phoneNumber: recipient.mobile,
        type: 'promotional',
        platform: 'whatsapp',
        templateId,
        content: {
          ...variables,
          recipientName: recipient.name || 'User'
        },
        status: 'pending',
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkWhatsapp: true,
          batchId,
          createdBy: creatorId,
          templateUsed: templateId
        }
      };

      notifications.push(notification);
    }

    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      const created = await Notifications.bulkCreate(chunk);
      createdNotifications = createdNotifications.concat(created);
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: 'queued',
        message: 'WhatsApp messages have been queued for delivery. They will be processed by the notification service.',
        estimatedDeliveryTime: '5-15 minutes',
        expiresAt,
        note: "WhatsApp integration is currently in development."
      }
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  getEmailTemplates,
  getSmsTemplates,
  getWhatsappTemplates,
  sendBulkEmail,
  sendBulkSms,
  sendBulkWhatsapp
};
