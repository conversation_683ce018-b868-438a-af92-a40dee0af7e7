const fetch = require("node-fetch");
const { config } = require("../../config/config");

/**
 * Send an SMS using MSG91 API
 * @param {Object} options - SMS options
 * @param {string} options.templateId - MSG91 template ID
 * @param {string} options.mobile - Recipient mobile number (with country code, e.g., 919XXXXXXXXX)
 * @param {Object} options.variables - Template variables (VAR1, VAR2, etc.)
 * @param {boolean} options.shortUrl - Whether to enable short URL (default: false)
 * @param {number} options.shortUrlExpiry - Short URL expiry in seconds (optional)
 * @returns {Promise<Object>} - MSG91 API response
 */
const sendSMS = async ({
  templateId,
  mobile,
  variables,
  shortUrl = false,
  shortUrlExpiry,
}) => {
  try {
    // Validate required fields
    if (!templateId || !mobile) {
      throw new Error("Missing required SMS parameters: templateId, mobile");
    }

    // Format the mobile number if needed
    const formattedMobile = formatMobileNumber(mobile);

    // Prepare the request payload
    const payload = {
      template_id: templateId,
      short_url: shortUrl ? 1 : 0,
      recipients: [
        {
          mobiles: formattedMobile,
          ...variables,
        },
      ],
    };

    // Add optional parameters if provided
    if (shortUrlExpiry) {
      payload.short_url_expiry = shortUrlExpiry;
    }
    console.log(payload);
    // Add real-time response parameter
    payload.realTimeResponse = 1;

    // Send the request to MSG91 API
    const response = await fetch(`${config.msg91.baseUrl}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        authkey: config.msg91.authKey,
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error("Error sending SMS:", data);
      throw new Error(`SMS sending failed: ${data.message || "Unknown error"}`);
    }
    return data;
  } catch (error) {
    console.error("Error in sendSMS:", error);
    throw error;
  }
};

/**
 * Format mobile number to ensure it has country code
 * @param {string} mobile - Mobile number
 * @returns {string} - Formatted mobile number
 */
const formatMobileNumber = (mobile) => {
  // Remove any non-digit characters
  const digits = mobile.replace(/\D/g, "");

  // If the number doesn't start with country code (e.g., 91 for India)
  // add the default country code (91)
  if (digits.length === 10) {
    return `91${digits}`;
  }

  return digits;
};

/**
 * Verify the SMS configuration
 * @returns {Promise<boolean>} - Whether the configuration is valid
 */
const verifyConfiguration = async () => {
  try {
    if (!config.msg91.authKey) {
      console.error("MSG91 Auth Key is not configured");
      return false;
    }
    return true;
  } catch (error) {
    console.error("SMS configuration verification failed:", error);
    return false;
  }
};

module.exports = {
  sendSMS,
  verifyConfiguration,
};
