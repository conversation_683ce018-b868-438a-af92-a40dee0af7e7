const initModels = (sequelize) => {
  const Tournament = require("./tournament")(sequelize);
  const User = require("./user")(sequelize);
  const PlayerDetail = require("./playerDetail")(sequelize);
  const ClubDetail = require("./clubDetail")(sequelize);
  const Registration = require("./registration")(sequelize);
  const Payment = require("./payment")(sequelize);
  const Bankdetails = require("./bankdetails")(sequelize);
  const InviteRequest = require("./inviteRequest")(sequelize);
  const BulkRegistration = require("./BulkRegistration")(sequelize);
  const Ranking = require("./ranking")(sequelize);
  const Pairing = require("./pairing")(sequelize);
  const Otp = require("./otp")(sequelize);
  const ArbiterDetails = require("./arbiterDetails")(sequelize);
  const Notifications = require("./notifications")(sequelize);
  const Content = require("./content")(sequelize);

  // const PlayerDetail = require("./playerDetail")(sequelize);

  // // Define associations
  // User.hasOne(PlayerDetail);
  // PlayerDetail.belongsTo(User);

  // Tournament.hasMany(User);
  // User.belongsToMany(Tournament, { through: 'UserTournaments' });

  return {
    Tournament,
    User,
    PlayerDetail,
    ClubDetail,
    Registration,
    Payment,
    Bankdetails,
    InviteRequest,
    BulkRegistration,
    Ranking,
    Pairing,
    Otp,
    ArbiterDetails,
    Notifications,
    Content,
  };
};

module.exports = initModels;
