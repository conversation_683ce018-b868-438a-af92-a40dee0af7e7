# Admin Messaging API Documentation

This document describes the API endpoints for the admin messaging system that allows bulk sending of emails, SMS, and WhatsApp messages with template support.

## Base URL
All endpoints are prefixed with `/api/v1/admin/` and require admin authentication.

## Authentication
All endpoints require a valid JWT token with admin role in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Template Management Endpoints

### 1. Get Email Templates
**GET** `/admin/email-templates`

Returns all available email templates organized by category.

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": {
      "tournament": [
        {
          "id": "tournament-registration",
          "name": "Tournament Registration",
          "category": "tournament",
          "description": "Tournament registration confirmation",
          "variables": ["playerName", "tournamentName", "startDate", "venue"],
          "content": "<!DOCTYPE html>..."
        }
      ],
      "club": [...],
      "payment": [...],
      "authentication": [...]
    },
    "totalTemplates": 25
  }
}
```

### 2. Get SMS Templates
**GET** `/admin/sms-templates`

Returns all available SMS templates organized by category.

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": {
      "tournament": [
        {
          "id": "registration-confirmation",
          "name": "Registration Confirmation",
          "category": "tournament",
          "description": "Tournament registration confirmation",
          "variables": ["VAR1", "VAR2", "VAR3"],
          "templateId": "",
          "maxLength": 160
        }
      ],
      "authentication": [...],
      "payment": [...],
      "club": [...]
    },
    "totalTemplates": 7
  }
}
```

### 3. Get WhatsApp Templates
**GET** `/admin/whatsapp-templates`

Returns all available WhatsApp templates organized by category.

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": {
      "tournament": [
        {
          "id": "tournament-registration",
          "name": "Tournament Registration",
          "category": "tournament",
          "description": "Tournament registration confirmation",
          "variables": ["playerName", "tournamentName", "startDate", "venue"],
          "maxLength": 4096
        }
      ],
      "user": [...],
      "payment": [...],
      "club": [...]
    },
    "totalTemplates": 4
  }
}
```

## Recipient Search Endpoints

### 1. Search Players
**GET** `/admin/players/search?page=1&limit=10&playerName=john&city=mumbai`

Search for players with various filters.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `playerName` (optional): Player name filter
- `playerId` (optional): Player ID filter
- `city` (optional): City filter
- `country` (optional): Country filter
- `state` (optional): State filter
- `district` (optional): District filter

### 2. Search Clubs
**GET** `/admin/clubs/search?page=1&limit=10&clubName=chess&city=delhi`

Search for clubs with various filters.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `clubName` (optional): Club name filter
- `city` (optional): City filter
- `country` (optional): Country filter
- `state` (optional): State filter
- `district` (optional): District filter

### 3. Search Arbiters
**GET** `/admin/arbiters/search?page=1&limit=10&arbiterName=smith`

Search for arbiters with various filters.

### 4. Search Tournaments
**GET** `/admin/tournaments/search?page=1&limit=10&title=championship&month=12&year=2024`

Search for tournaments with various filters.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `title` (optional): Tournament title filter
- `city` (optional): City filter
- `age` (optional): Age category filter
- `tournamentType` (optional): Tournament type filter
- `tournamentCategory` (optional): Tournament category filter
- `country` (optional): Country filter
- `state` (optional): State filter
- `district` (optional): District filter
- `month` (optional): Month filter
- `year` (optional): Year filter

## Bulk Messaging Endpoints

### 1. Send Bulk Email
**POST** `/admin/send-email`

Send bulk emails to multiple recipients using templates or custom content.

**Request Body:**
```json
{
  "recipients": [
    {
      "email": "<EMAIL>",
      "name": "John Doe",
      "id": "player123"
    },
    {
      "email": "<EMAIL>",
      "name": "Jane Smith",
      "id": "player456"
    }
  ],
  "subject": "Tournament Registration Confirmation",
  "templateName": "tournament-registration",
  "templateData": {
    "tournamentName": "National Chess Championship",
    "startDate": "2024-12-15",
    "venue": "Chess Club Mumbai"
  }
}
```

**Alternative with Custom Content:**
```json
{
  "recipients": [...],
  "subject": "Custom Message",
  "customContent": "<h1>Hello!</h1><p>This is a custom message.</p>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalRecipients": 2,
    "successful": 2,
    "failed": 0,
    "results": [
      {
        "recipient": "<EMAIL>",
        "status": "sent",
        "messageId": "msg_123456"
      }
    ],
    "errors": []
  }
}
```

### 2. Send Bulk SMS
**POST** `/admin/send-sms`

Send bulk SMS to multiple recipients using predefined templates only.

**Request Body:**
```json
{
  "recipients": [
    {
      "mobile": "919876543210",
      "name": "John Doe",
      "id": "player123"
    }
  ],
  "templateId": "registration_confirmation_template_id",
  "variables": {
    "VAR1": "John Doe",
    "VAR2": "National Chess Championship",
    "VAR3": "REG123456"
  }
}
```

### 3. Send Bulk WhatsApp
**POST** `/admin/send-whatsapp`

Send bulk WhatsApp messages to multiple recipients using predefined templates only.

**Request Body:**
```json
{
  "recipients": [
    {
      "mobile": "919876543210",
      "name": "John Doe",
      "id": "player123"
    }
  ],
  "templateId": "tournament_registration",
  "variables": {
    "playerName": "John Doe",
    "tournamentName": "National Chess Championship",
    "startDate": "2024-12-15",
    "venue": "Chess Club Mumbai"
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": [...] // Additional error details if available
}
```

## HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting

Bulk messaging endpoints are rate-limited to prevent abuse:
- Email: 100 recipients per request, 1000 emails per hour
- SMS: 50 recipients per request, 500 SMS per hour
- WhatsApp: 50 recipients per request, 500 messages per hour

## Template Variables

### Email Templates
Email templates support dynamic variables using Handlebars syntax:
- `{{name}}` - Recipient name
- `{{email}}` - Recipient email
- `{{currentYear}}` - Current year
- Custom variables passed in `templateData`

### SMS Templates
SMS templates use MSG91 variable format:
- `VAR1`, `VAR2`, `VAR3`, etc.
- Variables are replaced in order

### WhatsApp Templates
WhatsApp templates use named variables:
- `{playerName}`, `{tournamentName}`, etc.
- Variables are replaced by name

## Usage Examples

See the frontend integration examples in the next section.
