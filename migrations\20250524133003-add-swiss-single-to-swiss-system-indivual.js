'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Step 1: Add new enum values
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_tournament_tournament_type" ADD VALUE IF NOT EXISTS 'individual';
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_tournament_tournament_system" ADD VALUE IF NOT EXISTS 'swiss-system';
    `);

    // Step 2: Update rows using the new values
    await queryInterface.sequelize.query(`
      UPDATE tournament SET tournament_type = 'individual' WHERE tournament_type = 'single';
    `);

    await queryInterface.sequelize.query(`
      UPDATE tournament SET tournament_system = 'swiss-system' WHERE tournament_system = 'swiss';
    `);
  },

  async down(queryInterface, Sequelize) {
    // Step 1: Revert rows to old enum values
    await queryInterface.sequelize.query(`
      UPDATE tournament SET tournament_type = 'single' WHERE tournament_type = 'individual';
    `);

    await queryInterface.sequelize.query(`
      UPDATE tournament SET tournament_system = 'swiss' WHERE tournament_system = 'swiss-system';
    `);
  }
};
